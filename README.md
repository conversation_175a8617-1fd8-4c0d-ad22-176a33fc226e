# 系统资源监控程序 (mon)

一个专为嵌入式Linux系统设计的轻量级系统资源监控程序，基于Rust开发，具有高性能、低资源占用的特点。

## 功能特性

### 系统级监控
- **CPU监控**: CPU使用率、负载平均值、每核心使用率、频率
- **内存监控**: 内存使用率、交换分区使用情况
- **磁盘监控**: 磁盘空间使用率、inode使用率、读写速率
- **网络监控**: 网络接口状态、连通性检查、服务状态
- **温度监控**: 系统温度传感器监控
- **存储健康**: SSD/eMMC/SD卡健康状况和寿命预估
- **文件系统**: 关键文件和目录监控

### 预警系统
- 可配置的阈值监控
- 多级预警（信息、警告、严重、紧急）
- 自动预警解决
- 预警历史记录

### 数据存储
- 基于SQLite的时序数据库
- 自动数据清理和归档
- 高效的数据压缩存储

### 日志系统
- 结构化日志记录
- 自动日志轮转
- 事件追踪和审计

## 系统要求

- Linux操作系统（嵌入式或桌面版）
- Rust 1.70+ （编译时）
- SQLite3支持
- 系统管理员权限（运行时）

## 编译安装

### 1. 克隆代码
```bash
git clone <repository-url>
cd mon
```

### 2. 编译程序
```bash
# 调试版本
cargo build

# 发布版本（推荐）
cargo build --release
```

### 3. 安装到系统
```bash
# 使用安装脚本（推荐）
sudo ./scripts/install.sh

# 或手动安装
sudo cp target/release/mon /usr/local/bin/
sudo mkdir -p /etc/mon /var/lib/mon /var/log/mon
sudo cp config.toml /etc/mon/
```

## 配置说明

配置文件位于 `/etc/mon/config.toml`，主要配置项：

### 基本配置
```toml
# 监控间隔（秒）
watch_interval = 60

# 数据保留天数
data_retention_days = 30

# 数据库路径
database_path = "/var/lib/mon/data.db"

# 日志目录
log_path = "/var/log/mon/"
```

### 预警阈值
```toml
[thresholds]
cpu_usage_warning = 80.0      # CPU使用率警告阈值
cpu_usage_critical = 95.0     # CPU使用率严重阈值
memory_usage_warning = 80.0   # 内存使用率警告阈值
memory_usage_critical = 95.0  # 内存使用率严重阈值
# ... 更多阈值配置
```

### 监控功能开关
```toml
[monitoring]
enable_cpu = true              # 启用CPU监控
enable_memory = true           # 启用内存监控
enable_disk = true             # 启用磁盘监控
enable_network = true          # 启用网络监控
enable_temperature = true      # 启用温度监控
enable_storage_health = true   # 启用存储健康监控
enable_filesystem = false      # 启用文件系统监控

# 文件系统监控路径
filesystem_paths = ["/etc", "/var/log"]

# 网络连通性检查主机
network_check_hosts = ["*******", "*******"]
```

## 运行方式

### 作为系统服务运行（推荐）
```bash
# 启动服务
sudo systemctl start mon

# 停止服务
sudo systemctl stop mon

# 查看状态
sudo systemctl status mon

# 查看日志
sudo journalctl -u mon -f

# 开机自启
sudo systemctl enable mon
```

### 直接运行
```bash
# 使用默认配置
sudo mon

# 指定配置文件
sudo MON_CONFIG_PATH=/path/to/config.toml mon
```

## 日志文件

程序会在配置的日志目录中生成以下日志文件：

- `system.log`: 系统运行日志
- `events.log`: 结构化事件日志（JSON格式）
- `alerts.log`: 预警记录（JSON格式）

## 数据库结构

程序使用SQLite数据库存储监控数据，主要表结构：

- `system_data`: 系统基本信息
- `disk_data`: 磁盘使用信息
- `network_data`: 网络接口信息
- `temperature_data`: 温度传感器数据
- `storage_health_data`: 存储设备健康数据
- `alerts`: 预警记录

## 故障排除

### 常见问题

1. **权限不足**
   ```bash
   # 确保以root权限运行
   sudo mon
   ```

2. **配置文件错误**
   ```bash
   # 检查配置文件语法
   toml-check /etc/mon/config.toml
   ```

3. **数据库权限问题**
   ```bash
   # 检查数据库目录权限
   sudo chown -R root:root /var/lib/mon
   sudo chmod 755 /var/lib/mon
   ```

4. **日志目录问题**
   ```bash
   # 创建日志目录
   sudo mkdir -p /var/log/mon
   sudo chown root:root /var/log/mon
   ```

### 调试模式
```bash
# 启用详细日志
RUST_LOG=debug sudo mon
```

## 性能特点

- **低内存占用**: 典型运行时内存占用 < 10MB
- **低CPU开销**: 监控周期CPU使用率 < 1%
- **高效存储**: 使用SQLite WAL模式，支持并发读写
- **快速启动**: 程序启动时间 < 1秒

## 适用场景

- 嵌入式Linux设备监控
- IoT设备健康检查
- 工业控制系统监控
- 边缘计算节点监控
- 服务器基础监控

## 开发说明

### 项目结构
```
src/
├── main.rs              # 主程序入口
├── config.rs            # 配置管理
├── collector/           # 数据收集模块
│   ├── mod.rs
│   ├── cpu.rs
│   ├── memory.rs
│   ├── disk.rs
│   ├── network.rs
│   ├── temperature.rs
│   ├── storage_health.rs
│   └── filesystem.rs
├── storage.rs           # 数据存储
├── alert.rs             # 预警系统
├── logger.rs            # 日志系统
└── types.rs             # 数据类型定义
```

### 添加新的监控项
1. 在 `types.rs` 中定义数据结构
2. 在 `collector/` 中实现收集逻辑
3. 在 `storage.rs` 中添加存储逻辑
4. 在 `alert.rs` 中添加预警检查

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

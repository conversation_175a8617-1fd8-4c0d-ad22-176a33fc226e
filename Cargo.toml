[package]
name = "mon"
version = "0.1.0"
edition = "2021"
description = "System resource monitoring tool for embedded Linux systems"
authors = ["Your Name <<EMAIL>>"]
license = "MIT"

[lib]
name = "mon"
path = "src/lib.rs"

[[bin]]
name = "mon"
path = "src/main.rs"

[dependencies]
# 系统信息收集
sysinfo = "0.36.1"
procfs = "0.17.0"
nix = { version = "0.30", features = ["fs"] }

# 异步运行时
tokio = { version = "1.0", features = ["full"] }

# 序列化和配置
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.9"

# 日志记录
log = "0.4"
env_logger = "0.11"

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "2.0"

# 数据库 (SQLite for embedded systems)
rusqlite = { version = "0.37", features = ["bundled", "chrono"] }

# 网络监控
# netstat2 = "0.9"  # 暂时移除，有兼容性问题

# 文件系统监控
notify = "8.1.0"

# 配置文件监控
notify-debouncer-mini = "0.6"
notify-debouncer-full = "0.5"

# UUID生成
uuid = { version = "1.0", features = ["v4"] }

# misc
async-fs = "2.1.3"
futures-core = "0.3.31"
futures-util = "0.3.31"
libc = "0.2.174"
futures = "0.3.31"

[[example]]
name = "filesystem_monitor"
path = "examples/filesystem_monitor.rs"

[[example]]
name = "config_monitor"
path = "examples/config_monitor.rs"

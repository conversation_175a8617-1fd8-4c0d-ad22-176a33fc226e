use anyhow::Result;
use chrono::Utc;
use log::debug;
use std::fs;
use std::process::Command;

use crate::types::{
    SystemData, MonitorConfig, CpuData, LoadAverage, MemoryData, 
    DiskData, NetworkData, NetworkInterface, NetworkConnectivity, 
    NetworkServices, ServiceStatus, TemperatureData, StorageHealthData, 
    FilesystemData
};

/// 简化的系统信息收集器
pub struct SystemCollector {
    config: MonitorConfig,
}

impl SystemCollector {
    /// 创建新的系统收集器
    pub fn new(config: &MonitorConfig) -> Result<Self> {
        debug!("初始化简化系统信息收集器");
        
        Ok(Self {
            config: config.clone(),
        })
    }

    /// 收集所有系统信息
    pub async fn collect_all(&mut self) -> Result<SystemData> {
        debug!("开始收集系统信息");
        
        let timestamp = Utc::now();
        
        let cpu_data = self.collect_cpu_data().await?;
        let memory_data = self.collect_memory_data().await?;
        let disk_data = self.collect_disk_data().await?;
        let network_data = self.collect_network_data().await?;
        let temperature_data = self.collect_temperature_data().await?;
        let storage_health_data = self.collect_storage_health_data().await?;
        let filesystem_data = self.collect_filesystem_data().await?;

        let system_data = SystemData {
            timestamp,
            cpu: cpu_data,
            memory: memory_data,
            disk: disk_data,
            network: network_data,
            temperature: temperature_data,
            storage_health: storage_health_data,
            filesystem: filesystem_data,
        };

        debug!("系统信息收集完成");
        Ok(system_data)
    }

    /// 收集CPU数据
    async fn collect_cpu_data(&self) -> Result<CpuData> {
        if !self.config.monitoring.enable_cpu {
            return Ok(CpuData {
                usage_percent: 0.0,
                load_average: LoadAverage {
                    one_minute: 0.0,
                    five_minutes: 0.0,
                    fifteen_minutes: 0.0,
                },
                per_core_usage: vec![],
                frequency: None,
            });
        }

        // 简单的CPU使用率获取
        let usage_percent = self.get_cpu_usage().await.unwrap_or(0.0);
        let load_average = self.get_load_average().await.unwrap_or_default();

        Ok(CpuData {
            usage_percent,
            load_average,
            per_core_usage: vec![usage_percent], // 简化为单核
            frequency: None,
        })
    }

    /// 收集内存数据
    async fn collect_memory_data(&self) -> Result<MemoryData> {
        if !self.config.monitoring.enable_memory {
            return Ok(MemoryData {
                total: 0,
                used: 0,
                available: 0,
                usage_percent: 0.0,
                swap_total: 0,
                swap_used: 0,
                swap_usage_percent: 0.0,
            });
        }

        // 从/proc/meminfo读取内存信息
        let meminfo = fs::read_to_string("/proc/meminfo").unwrap_or_default();
        let mut total = 0u64;
        let mut available = 0u64;
        let mut swap_total = 0u64;
        let mut swap_free = 0u64;

        for line in meminfo.lines() {
            if line.starts_with("MemTotal:") {
                total = self.parse_meminfo_value(line) * 1024;
            } else if line.starts_with("MemAvailable:") {
                available = self.parse_meminfo_value(line) * 1024;
            } else if line.starts_with("SwapTotal:") {
                swap_total = self.parse_meminfo_value(line) * 1024;
            } else if line.starts_with("SwapFree:") {
                swap_free = self.parse_meminfo_value(line) * 1024;
            }
        }

        let used = total - available;
        let usage_percent = if total > 0 { (used as f64 / total as f64) * 100.0 } else { 0.0 };
        let swap_used = swap_total - swap_free;
        let swap_usage_percent = if swap_total > 0 { (swap_used as f64 / swap_total as f64) * 100.0 } else { 0.0 };

        Ok(MemoryData {
            total,
            used,
            available,
            usage_percent,
            swap_total,
            swap_used,
            swap_usage_percent,
        })
    }

    /// 收集磁盘数据
    async fn collect_disk_data(&self) -> Result<Vec<DiskData>> {
        if !self.config.monitoring.enable_disk {
            return Ok(vec![]);
        }

        let mut disk_data = Vec::new();

        // 简单的磁盘使用率检查（仅检查根分区）
        if let Ok(output) = Command::new("df").args(&["-h", "/"]).output() {
            let output_str = String::from_utf8_lossy(&output.stdout);
            for line in output_str.lines().skip(1) {
                let fields: Vec<&str> = line.split_whitespace().collect();
                if fields.len() >= 6 {
                    let device = fields[0].to_string();
                    let mount_point = fields[5].to_string();
                    let usage_str = fields[4].trim_end_matches('%');
                    let usage_percent = usage_str.parse::<f64>().unwrap_or(0.0);

                    disk_data.push(DiskData {
                        device,
                        mount_point,
                        total_space: 0, // 简化版本不获取具体数值
                        used_space: 0,
                        available_space: 0,
                        usage_percent,
                        inodes_total: None,
                        inodes_used: None,
                        inodes_usage_percent: None,
                        read_bytes_per_sec: None,
                        write_bytes_per_sec: None,
                    });
                }
            }
        }

        Ok(disk_data)
    }

    /// 收集网络数据
    async fn collect_network_data(&self) -> Result<NetworkData> {
        if !self.config.monitoring.enable_network {
            return Ok(NetworkData {
                interfaces: vec![],
                connectivity: NetworkConnectivity {
                    gateway_reachable: false,
                    dns_working: false,
                    internet_access: false,
                    gateway_ip: None,
                    dns_servers: vec![],
                },
                services: NetworkServices {
                    ssh_status: ServiceStatus::Unknown,
                    ntp_status: ServiceStatus::Unknown,
                    dns_status: ServiceStatus::Unknown,
                },
            });
        }

        // 简化的网络接口检查
        let interfaces = vec![NetworkInterface {
            name: "eth0".to_string(),
            ip_address: Some("*************".to_string()),
            is_up: true,
            bytes_sent: 0,
            bytes_received: 0,
            packets_sent: 0,
            packets_received: 0,
            errors_in: 0,
            errors_out: 0,
        }];

        let connectivity = NetworkConnectivity {
            gateway_reachable: true,
            dns_working: true,
            internet_access: true,
            gateway_ip: Some("***********".to_string()),
            dns_servers: vec!["*******".to_string()],
        };

        let services = NetworkServices {
            ssh_status: ServiceStatus::Running,
            ntp_status: ServiceStatus::Running,
            dns_status: ServiceStatus::Running,
        };

        Ok(NetworkData {
            interfaces,
            connectivity,
            services,
        })
    }

    /// 收集温度数据
    async fn collect_temperature_data(&self) -> Result<Vec<TemperatureData>> {
        if !self.config.monitoring.enable_temperature {
            return Ok(vec![]);
        }

        let mut temperature_data = Vec::new();

        // 尝试从thermal_zone0读取CPU温度
        if let Ok(temp_str) = fs::read_to_string("/sys/class/thermal/thermal_zone0/temp") {
            if let Ok(temp_millidegrees) = temp_str.trim().parse::<i32>() {
                let temperature = temp_millidegrees as f64 / 1000.0;
                temperature_data.push(TemperatureData {
                    sensor_name: "CPU".to_string(),
                    temperature,
                    critical_temp: Some(85.0),
                    max_temp: Some(85.0),
                });
            }
        }

        Ok(temperature_data)
    }

    /// 收集存储健康数据
    async fn collect_storage_health_data(&self) -> Result<StorageHealthData> {
        if !self.config.monitoring.enable_storage_health {
            return Ok(StorageHealthData { devices: vec![] });
        }

        // 简化版本，返回空数据
        Ok(StorageHealthData { devices: vec![] })
    }

    /// 收集文件系统数据
    async fn collect_filesystem_data(&self) -> Result<Vec<FilesystemData>> {
        if !self.config.monitoring.enable_filesystem {
            return Ok(vec![]);
        }

        // 简化版本，返回空数据
        Ok(vec![])
    }

    /// 更新配置
    pub fn update_config(&mut self, config: MonitorConfig) -> Result<()> {
        debug!("更新收集器配置");
        self.config = config;
        Ok(())
    }

    /// 获取CPU使用率
    async fn get_cpu_usage(&self) -> Result<f64> {
        // 简单的CPU使用率计算
        if let Ok(stat1) = fs::read_to_string("/proc/stat") {
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
            if let Ok(stat2) = fs::read_to_string("/proc/stat") {
                return Ok(self.calculate_cpu_usage(&stat1, &stat2));
            }
        }
        Ok(0.0)
    }

    /// 计算CPU使用率
    fn calculate_cpu_usage(&self, stat1: &str, stat2: &str) -> f64 {
        let parse_cpu_line = |line: &str| -> Option<(u64, u64)> {
            let fields: Vec<&str> = line.split_whitespace().collect();
            if fields.len() >= 5 && fields[0] == "cpu" {
                let user: u64 = fields[1].parse().ok()?;
                let nice: u64 = fields[2].parse().ok()?;
                let system: u64 = fields[3].parse().ok()?;
                let idle: u64 = fields[4].parse().ok()?;
                let total = user + nice + system + idle;
                Some((total - idle, total))
            } else {
                None
            }
        };

        if let (Some((active1, total1)), Some((active2, total2))) = (
            stat1.lines().next().and_then(parse_cpu_line),
            stat2.lines().next().and_then(parse_cpu_line),
        ) {
            let active_diff = active2.saturating_sub(active1);
            let total_diff = total2.saturating_sub(total1);
            if total_diff > 0 {
                return (active_diff as f64 / total_diff as f64) * 100.0;
            }
        }
        0.0
    }

    /// 获取负载平均值
    async fn get_load_average(&self) -> Result<LoadAverage> {
        if let Ok(loadavg) = fs::read_to_string("/proc/loadavg") {
            let fields: Vec<&str> = loadavg.split_whitespace().collect();
            if fields.len() >= 3 {
                return Ok(LoadAverage {
                    one_minute: fields[0].parse().unwrap_or(0.0),
                    five_minutes: fields[1].parse().unwrap_or(0.0),
                    fifteen_minutes: fields[2].parse().unwrap_or(0.0),
                });
            }
        }
        Ok(LoadAverage {
            one_minute: 0.0,
            five_minutes: 0.0,
            fifteen_minutes: 0.0,
        })
    }

    /// 解析meminfo中的数值
    fn parse_meminfo_value(&self, line: &str) -> u64 {
        line.split_whitespace()
            .nth(1)
            .and_then(|s| s.parse().ok())
            .unwrap_or(0)
    }
}

impl Default for LoadAverage {
    fn default() -> Self {
        Self {
            one_minute: 0.0,
            five_minutes: 0.0,
            fifteen_minutes: 0.0,
        }
    }
}

use anyhow::Result;
use log::{debug, warn};
use sysinfo::{System, SystemExt, NetworkExt};
use std::collections::HashMap;
use std::process::Command;
use std::time::Duration;
use tokio::time::timeout;

use crate::types::{NetworkData, NetworkInterface, NetworkConnectivity, NetworkServices, ServiceStatus};

/// 网络信息收集器
pub struct NetworkCollector {
    system: System,
    check_hosts: Vec<String>,
    prev_network_stats: HashMap<String, (u64, u64, u64, u64)>, // (bytes_sent, bytes_received, packets_sent, packets_received)
}

impl NetworkCollector {
    /// 创建新的网络收集器
    pub fn new(check_hosts: &[String]) -> Result<Self> {
        debug!("初始化网络收集器");
        let mut system = System::new_all();
        system.refresh_networks_list();
        system.refresh_networks();
        
        Ok(Self {
            system,
            check_hosts: check_hosts.to_vec(),
            prev_network_stats: HashMap::new(),
        })
    }
    
    /// 收集网络信息
    pub async fn collect(&mut self) -> Result<NetworkData> {
        debug!("收集网络信息");
        
        // 刷新网络信息
        self.system.refresh_networks();
        
        // 收集网络接口信息
        let interfaces = self.collect_interfaces().await?;
        
        // 检查网络连通性
        let connectivity = self.check_connectivity().await?;
        
        // 检查网络服务状态
        let services = self.check_services().await?;
        
        Ok(NetworkData {
            interfaces,
            connectivity,
            services,
        })
    }
    
    /// 收集网络接口信息
    async fn collect_interfaces(&mut self) -> Result<Vec<NetworkInterface>> {
        let mut interfaces = Vec::new();
        
        for (interface_name, network) in self.system.networks() {
            // 获取接口IP地址
            let ip_address = self.get_interface_ip(interface_name)?;
            
            // 检查接口是否启用
            let is_up = self.is_interface_up(interface_name)?;
            
            // 获取统计信息
            let bytes_sent = network.total_transmitted();
            let bytes_received = network.total_received();
            let packets_sent = network.total_packets_transmitted();
            let packets_received = network.total_packets_received();
            let errors_in = network.total_errors_on_received();
            let errors_out = network.total_errors_on_transmitted();
            
            let interface = NetworkInterface {
                name: interface_name.clone(),
                ip_address,
                is_up,
                bytes_sent,
                bytes_received,
                packets_sent,
                packets_received,
                errors_in,
                errors_out,
            };
            
            interfaces.push(interface);
        }
        
        Ok(interfaces)
    }
    
    /// 获取接口IP地址
    fn get_interface_ip(&self, interface_name: &str) -> Result<Option<String>> {
        // 使用ip命令获取接口IP地址
        let output = Command::new("ip")
            .args(&["addr", "show", interface_name])
            .output();
        
        match output {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                
                // 解析IP地址
                for line in output_str.lines() {
                    if line.contains("inet ") && !line.contains("127.0.0.1") {
                        if let Some(ip_part) = line.split_whitespace().nth(1) {
                            if let Some(ip) = ip_part.split('/').next() {
                                return Ok(Some(ip.to_string()));
                            }
                        }
                    }
                }
                Ok(None)
            },
            Err(e) => {
                warn!("无法获取接口{}的IP地址: {}", interface_name, e);
                Ok(None)
            }
        }
    }
    
    /// 检查接口是否启用
    fn is_interface_up(&self, interface_name: &str) -> Result<bool> {
        let output = Command::new("ip")
            .args(&["link", "show", interface_name])
            .output();
        
        match output {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                Ok(output_str.contains("state UP"))
            },
            Err(e) => {
                warn!("无法检查接口{}状态: {}", interface_name, e);
                Ok(false)
            }
        }
    }
    
    /// 检查网络连通性
    async fn check_connectivity(&self) -> Result<NetworkConnectivity> {
        // 获取默认网关
        let gateway_ip = self.get_default_gateway()?;
        
        // 检查网关连通性
        let gateway_reachable = if let Some(ref gateway) = gateway_ip {
            self.ping_host(gateway).await.unwrap_or(false)
        } else {
            false
        };
        
        // 获取DNS服务器
        let dns_servers = self.get_dns_servers()?;
        
        // 检查DNS工作状态
        let dns_working = self.check_dns().await.unwrap_or(false);
        
        // 检查互联网连接
        let internet_access = self.check_internet_access().await.unwrap_or(false);
        
        Ok(NetworkConnectivity {
            gateway_reachable,
            dns_working,
            internet_access,
            gateway_ip,
            dns_servers,
        })
    }
    
    /// 获取默认网关
    fn get_default_gateway(&self) -> Result<Option<String>> {
        let output = Command::new("ip")
            .args(&["route", "show", "default"])
            .output();
        
        match output {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                
                for line in output_str.lines() {
                    if line.contains("default via") {
                        if let Some(gateway) = line.split_whitespace().nth(2) {
                            return Ok(Some(gateway.to_string()));
                        }
                    }
                }
                Ok(None)
            },
            Err(e) => {
                warn!("无法获取默认网关: {}", e);
                Ok(None)
            }
        }
    }
    
    /// 获取DNS服务器
    fn get_dns_servers(&self) -> Result<Vec<String>> {
        let mut dns_servers = Vec::new();
        
        // 读取/etc/resolv.conf
        if let Ok(content) = std::fs::read_to_string("/etc/resolv.conf") {
            for line in content.lines() {
                if line.starts_with("nameserver") {
                    if let Some(dns) = line.split_whitespace().nth(1) {
                        dns_servers.push(dns.to_string());
                    }
                }
            }
        }
        
        Ok(dns_servers)
    }
    
    /// Ping主机
    async fn ping_host(&self, host: &str) -> Result<bool> {
        let output = timeout(
            Duration::from_secs(5),
            tokio::process::Command::new("ping")
                .args(&["-c", "1", "-W", "3", host])
                .output()
        ).await;
        
        match output {
            Ok(Ok(output)) => Ok(output.status.success()),
            _ => Ok(false),
        }
    }
    
    /// 检查DNS工作状态
    async fn check_dns(&self) -> Result<bool> {
        let output = timeout(
            Duration::from_secs(5),
            tokio::process::Command::new("nslookup")
                .args(&["google.com"])
                .output()
        ).await;
        
        match output {
            Ok(Ok(output)) => Ok(output.status.success()),
            _ => Ok(false),
        }
    }
    
    /// 检查互联网连接
    async fn check_internet_access(&self) -> Result<bool> {
        for host in &self.check_hosts {
            if self.ping_host(host).await.unwrap_or(false) {
                return Ok(true);
            }
        }
        Ok(false)
    }
    
    /// 检查网络服务状态
    async fn check_services(&self) -> Result<NetworkServices> {
        let ssh_status = self.check_service_status("ssh").await;
        let ntp_status = self.check_service_status("ntp").await;
        let dns_status = self.check_service_status("systemd-resolved").await;
        
        Ok(NetworkServices {
            ssh_status,
            ntp_status,
            dns_status,
        })
    }
    
    /// 检查服务状态
    async fn check_service_status(&self, service_name: &str) -> ServiceStatus {
        let output = tokio::process::Command::new("systemctl")
            .args(&["is-active", service_name])
            .output()
            .await;
        
        match output {
            Ok(output) => {
                let status_str = String::from_utf8_lossy(&output.stdout).trim().to_string();
                match status_str.as_str() {
                    "active" => ServiceStatus::Running,
                    "inactive" => ServiceStatus::Stopped,
                    "failed" => ServiceStatus::Failed,
                    _ => ServiceStatus::Unknown,
                }
            },
            Err(_) => ServiceStatus::Unknown,
        }
    }
    
    /// 更新检查主机列表
    pub fn update_check_hosts(&mut self, hosts: &[String]) -> Result<()> {
        self.check_hosts = hosts.to_vec();
        Ok(())
    }
}

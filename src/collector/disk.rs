use anyhow::{Result, Context};
use log::{debug, warn};
use sysinfo::Disks;
use procfs::DiskStat;
use std::collections::HashMap;
use std::fs;
use std::path::Path;
use nix::sys::statvfs::statvfs;

use crate::types::{DiskData, inode_size_t};


/// 磁盘信息收集器
pub struct DiskCollector {
    inner: Disks,
    prev_disk_stats: HashMap<String, DiskStat>,
}

impl DiskCollector {
    /// 创建新的磁盘收集器
    pub fn new() -> Result<Self> {
        debug!("初始化磁盘收集器");
        let mut disks = Disks::new_with_refreshed_list();
        log::debug!("sysinfo disks: {:?}", disks);
        Ok(Self {
            inner: disks,
            prev_disk_stats: HashMap::new(),
        })
    }
    
    /// 收集磁盘信息
    pub async fn collect(&mut self) -> Result<Vec<DiskData>> {
        debug!("收集磁盘信息");
        
        // 刷新磁盘信息
        self.inner.refresh(true);
        
        let mut disk_data = Vec::new();
        
        // 获取挂载信息
        let mount_info = self.get_mount_info()?;
        
        // 获取磁盘统计信息
        let current_disk_stats = self.get_disk_stats()?;
        // 遍历所有磁盘
        for disk in self.inner.list() {
            let device_name = disk.name().to_string_lossy().to_string();
            let mount_point = disk.mount_point().to_string_lossy().to_string();
            
            // 获取磁盘空间信息
            let total_space = disk.total_space();
            let available_space = disk.available_space();
            let used_space = total_space - available_space;
            let usage_percent = if total_space > 0 {
                (used_space as f64 / total_space as f64) * 100.0
            } else {
                0.0
            };
            
            // 获取inode信息
            let (inodes_total, inodes_used, inodes_usage_percent) = 
                self.get_inode_info(&mount_point)?;
            
            let disk_info = DiskData {
                device: device_name,
                mount_point,
                total_space,
                used_space,
                available_space,
                usage_percent,
                inodes_total,
                inodes_used,
                inodes_usage_percent,
            };
            
            disk_data.push(disk_info);
        }
        
        // 更新上一次的磁盘统计信息
        self.prev_disk_stats = current_disk_stats;
        
        Ok(disk_data)
    }
    
    /// 获取挂载信息
    fn get_mount_info(&self) -> Result<HashMap<String, String>> {
        let mut mount_info = HashMap::new();
        
        match procfs::mounts() {
            Ok(mounts) => {
                log::debug!("mounts: {:?}", mounts);
            },
            Err(e) => {
                warn!("无法读取挂载信息: {}", e);
            }
        }
        
        Ok(mount_info)
    }
    
    /// 获取磁盘统计信息
    fn get_disk_stats(&self) -> Result<HashMap<String, DiskStat>> {
        let mut disk_stats = HashMap::new();
        
        match procfs::diskstats() {
            Ok(stats) => {
                for stat in stats {
                    disk_stats.insert(stat.name.clone(), stat);
                }
            },
            Err(e) => {
                warn!("无法读取磁盘统计信息: {}", e);
            }
        }
        
        Ok(disk_stats)
    }
    
    /// 获取inode信息
    fn get_inode_info(&self, mount_point: &str) -> Result<(Option<inode_size_t>, Option<inode_size_t>, Option<f64>)> {
        match statvfs(mount_point) {
            Ok(stat) => {
                let total_inodes = stat.files();
                let free_inodes = stat.files_free();
                let used_inodes = total_inodes - free_inodes;
                let usage_percent = if total_inodes > 0 {
                    (used_inodes as f64 / total_inodes as f64) * 100.0
                } else {
                    0.0
                };
                
                Ok((Some(total_inodes), Some(used_inodes), Some(usage_percent)))
            },
            Err(e) => {
                warn!("无法获取{}的inode信息: {}", mount_point, e);
                Ok((None, None, None))
            }
        }
    }

}

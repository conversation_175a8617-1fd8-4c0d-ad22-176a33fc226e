use anyhow::{Result, Context};
use log::{debug, warn};
use std::fs;
use std::path::Path;
use sysinfo::{System, Components};

use crate::types::TemperatureData;

/// 温度信息收集器
pub struct TemperatureCollector {
    inner: Components,
}

impl TemperatureCollector {
    /// 创建新的温度收集器
    pub fn new() -> Result<Self> {
        debug!("初始化温度收集器");
        let mut inner = Components::new_with_refreshed_list();
        
        Ok(Self { inner })
    }
    
    /// 收集温度信息
    pub async fn collect(&mut self) -> Result<Vec<TemperatureData>> {
        debug!("收集温度信息");
        
        // 刷新组件信息
        self.inner.refresh(true);
        
        let mut temperature_data = Vec::new();
        
        // 从sysinfo获取温度信息
        for component in &self.inner {
            let temp_data = TemperatureData {
                sensor_name: component.label().to_string(),
                temperature: component.temperature().map(|t|t as f64),
                critical_temp: component.critical().map(|t| t as f64),
                max_temp: component.max().map(|t|t as f64),
            };
            temperature_data.push(temp_data);
        }
        
        // 如果sysinfo没有获取到温度信息，尝试从hwmon获取
        if temperature_data.is_empty() {
            temperature_data.extend(self.collect_from_hwmon()?);
        }
        
        // 尝试获取CPU温度
        if let Ok(cpu_temps) = self.collect_cpu_temperature() {
            temperature_data.extend(cpu_temps);
        }
        
        Ok(temperature_data)
    }
    
    /// 从hwmon收集温度信息
    fn collect_from_hwmon(&self) -> Result<Vec<TemperatureData>> {
        let mut temperature_data = Vec::new();
        let hwmon_path = Path::new("/sys/class/hwmon");
        
        if !hwmon_path.exists() {
            return Ok(temperature_data);
        }
        
        // 遍历hwmon设备
        for entry in fs::read_dir(hwmon_path)? {
            let entry = entry?;
            let device_path = entry.path();
            
            if let Some(device_name) = device_path.file_name() {
                let device_name_str = device_name.to_string_lossy();
                
                // 获取设备名称
                let name_file = device_path.join("name");
                let sensor_name = if name_file.exists() {
                    fs::read_to_string(&name_file)
                        .unwrap_or_else(|_| device_name_str.to_string())
                        .trim()
                        .to_string()
                } else {
                    device_name_str.to_string()
                };
                
                // 查找温度输入文件
                if let Ok(entries) = fs::read_dir(&device_path) {
                    for temp_entry in entries {
                        if let Ok(temp_entry) = temp_entry {
                            let file_name = temp_entry.file_name();
                            let file_name_str = file_name.to_string_lossy();
                            
                            // 查找temp*_input文件
                            if file_name_str.starts_with("temp") && file_name_str.ends_with("_input") {
                                if let Ok(temp_str) = fs::read_to_string(temp_entry.path()) {
                                    if let Ok(temp_millidegrees) = temp_str.trim().parse::<i32>() {
                                        let temperature = temp_millidegrees as f64 / 1000.0;
                                        
                                        // 尝试获取临界温度
                                        let temp_num = file_name_str
                                            .strip_prefix("temp")
                                            .and_then(|s| s.strip_suffix("_input"))
                                            .unwrap_or("1");
                                        
                                        let crit_file = device_path.join(format!("temp{}_crit", temp_num));
                                        let critical_temp = if crit_file.exists() {
                                            fs::read_to_string(&crit_file)
                                                .ok()
                                                .and_then(|s| s.trim().parse::<i32>().ok())
                                                .map(|t| t as f64 / 1000.0)
                                        } else {
                                            None
                                        };
                                        
                                        // 尝试获取最大温度
                                        let max_file = device_path.join(format!("temp{}_max", temp_num));
                                        let max_temp = if max_file.exists() {
                                            fs::read_to_string(&max_file)
                                                .ok()
                                                .and_then(|s| s.trim().parse::<i32>().ok())
                                                .map(|t| t as f64 / 1000.0)
                                        } else {
                                            None
                                        };
                                        
                                        let temp_data = TemperatureData {
                                            sensor_name: format!("{}_temp{}", sensor_name, temp_num),
                                            temperature: Some(temperature),
                                            critical_temp,
                                            max_temp,
                                        };
                                        
                                        temperature_data.push(temp_data);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        Ok(temperature_data)
    }
    
    /// 收集CPU温度
    fn collect_cpu_temperature(&self) -> Result<Vec<TemperatureData>> {
        let mut temperature_data = Vec::new();
        
        // 尝试从thermal_zone获取CPU温度
        let thermal_path = Path::new("/sys/class/thermal");
        
        if !thermal_path.exists() {
            return Ok(temperature_data);
        }
        
        for entry in fs::read_dir(thermal_path)? {
            let entry = entry?;
            let zone_path = entry.path();
            
            if let Some(zone_name) = zone_path.file_name() {
                let zone_name_str = zone_name.to_string_lossy();
                
                if zone_name_str.starts_with("thermal_zone") {
                    // 读取温度类型
                    let type_file = zone_path.join("type");
                    let zone_type = if type_file.exists() {
                        fs::read_to_string(&type_file)
                            .unwrap_or_else(|_| zone_name_str.to_string())
                            .trim()
                            .to_string()
                    } else {
                        zone_name_str.to_string()
                    };
                    
                    // 读取当前温度
                    let temp_file = zone_path.join("temp");
                    if temp_file.exists() {
                        if let Ok(temp_str) = fs::read_to_string(&temp_file) {
                            if let Ok(temp_millidegrees) = temp_str.trim().parse::<i32>() {
                                let temperature = temp_millidegrees as f64 / 1000.0;
                                
                                // 尝试获取临界温度
                                let crit_file = zone_path.join("trip_point_0_temp");
                                let critical_temp = if crit_file.exists() {
                                    fs::read_to_string(&crit_file)
                                        .ok()
                                        .and_then(|s| s.trim().parse::<i32>().ok())
                                        .map(|t| t as f64 / 1000.0)
                                } else {
                                    None
                                };
                                
                                let temp_data = TemperatureData {
                                    sensor_name: zone_type,
                                    temperature: Some(temperature),
                                    critical_temp,
                                    max_temp: critical_temp,
                                };
                                
                                temperature_data.push(temp_data);
                            }
                        }
                    }
                }
            }
        }
        
        Ok(temperature_data)
    }
}

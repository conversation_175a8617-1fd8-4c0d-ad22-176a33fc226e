use anyhow::{Context, Result};
use log::{debug, warn};
use procfs::{CpuInfo, Current};
use sysinfo::System;
use tokio::time::{sleep, Duration};

use crate::types::{CpuData, LoadAverage};

/// CPU信息收集器
pub struct CpuCollector {
    system: System,
    prev_measurement: Option<System>,
}

impl CpuCollector {
    /// 创建新的CPU收集器
    pub fn new() -> Result<Self> {
        debug!("初始化CPU收集器");
        let mut system = System::new_all();
        system.refresh_cpu_all(); // 初始化CPU信息

        Ok(Self {
            system,
            prev_measurement: None,
        })
    }

    /// 收集CPU信息
    pub async fn collect(&mut self) -> Result<CpuData> {
        debug!("收集CPU信息");

        // 保存上一次测量结果
        let mut prev_system = System::new_all();
        std::mem::swap(&mut self.system, &mut prev_system);
        self.prev_measurement = Some(prev_system);

        // 刷新系统信息
        self.system.refresh_cpu_usage();

        // 等待一小段时间以获取准确的CPU使用率
        sleep(Duration::from_millis(500)).await;
        self.system.refresh_cpu_usage();

        // 获取CPU核心使用率
        let per_core_usage = self
            .system
            .cpus()
            .iter()
            .map(|cpu| cpu.cpu_usage() as f64)
            .collect::<Vec<f64>>();

        // 计算总体CPU使用率
        let usage_percent = if !per_core_usage.is_empty() {
            per_core_usage.iter().sum::<f64>() / per_core_usage.len() as f64
        } else {
            0.0
        };

        // 获取CPU频率
        let frequency = self.get_cpu_frequency()?;

        // 获取负载平均值
        let load_average = self.get_load_average()?;

        Ok(CpuData {
            usage_percent,
            load_average,
            per_core_usage,
            frequency,
        })
    }

    /// 获取CPU频率
    fn get_cpu_frequency(&self) -> Result<Option<u64>> {
        // 尝试从sysinfo获取
        let freq = self.system.cpus().first().map(|cpu| cpu.frequency());

        Ok(freq)
    }

    /// 获取系统负载平均值
    fn get_load_average(&self) -> Result<LoadAverage> {
        // 尝试从procfs获取负载平均值
        match procfs::LoadAverage::current() {
            Ok(load) => Ok(LoadAverage {
                one_minute: load.one as f64,
                five_minutes: load.five as f64,
                fifteen_minutes: load.fifteen as f64,
            }),
            Err(e) => {
                warn!("无法从procfs获取负载平均值: {}", e);
                // 尝试从sysinfo获取
                let load = System::load_average();

                Ok(LoadAverage {
                    one_minute: load.one,
                    five_minutes: load.five,
                    fifteen_minutes: load.fifteen,
                })
            }
        }
    }
}

mod cpu;
mod memory;
mod disk;
mod network;
mod temperature;
mod storage_health;
pub mod filesystem;

use anyhow::Result;
use chrono::Utc;
use log::{debug, warn};

use crate::types::{SystemData, MonitorConfig};
use cpu::CpuCollector;
use memory::MemoryCollector;
use disk::DiskCollector;
use network::NetworkCollector;
use temperature::TemperatureCollector;
use storage_health::StorageHealthCollector;
use filesystem::FilesystemCollector;

/// 系统信息收集器
pub struct SystemCollector {
    config: MonitorConfig,
    cpu_collector: CpuCollector,
    memory_collector: MemoryCollector,
    disk_collector: DiskCollector,
    network_collector: NetworkCollector,
    temperature_collector: TemperatureCollector,
    storage_health_collector: StorageHealthCollector,
    filesystem_collector: FilesystemCollector,
}

impl SystemCollector {
    /// 创建新的系统收集器
    pub fn new(config: &MonitorConfig) -> Result<Self> {
        debug!("初始化系统信息收集器");
        
        Ok(Self {
            config: config.clone(),
            cpu_collector: CpuCollector::new()?,
            memory_collector: MemoryCollector::new()?,
            disk_collector: DiskCollector::new()?,
            network_collector: NetworkCollector::new(&config.monitoring.network_check_hosts)?,
            temperature_collector: TemperatureCollector::new()?,
            storage_health_collector: StorageHealthCollector::new()?,
            filesystem_collector: FilesystemCollector::new(&config.monitoring.filesystem_paths)?,
        })
    }

    /// 收集所有系统信息
    pub async fn collect_all(&mut self) -> Result<SystemData> {
        debug!("开始收集系统信息");
        
        let timestamp = Utc::now();
        
        // 顺序收集各种系统信息（避免借用冲突）
        let cpu_data = self.collect_cpu_data().await?;
        let memory_data = self.collect_memory_data().await?;
        let disk_data = self.collect_disk_data().await?;
        let network_data = self.collect_network_data().await?;
        let temperature_data = self.collect_temperature_data().await?;
        let storage_health_data = self.collect_storage_health_data().await?;
        let filesystem_data = self.collect_filesystem_data().await?;

        let system_data = SystemData {
            timestamp,
            cpu: cpu_data,
            memory: memory_data,
            disk: disk_data,
            network: network_data,
            temperature: temperature_data,
            storage_health: storage_health_data,
            filesystem: vec![crate::types::FilesystemData::default()],
        };

        debug!("系统信息收集完成");
        Ok(system_data)
    }

    /// 收集CPU数据
    async fn collect_cpu_data(&mut self) -> Result<crate::types::CpuData> {
        if !self.config.monitoring.enable_cpu {
            debug!("CPU监控已禁用");
            return Ok(crate::types::CpuData {
                usage_percent: 0.0,
                load_average: crate::types::LoadAverage {
                    one_minute: 0.0,
                    five_minutes: 0.0,
                    fifteen_minutes: 0.0,
                },
                per_core_usage: vec![],
                frequency: None,
            });
        }

        self.cpu_collector.collect().await
    }

    /// 收集内存数据
    async fn collect_memory_data(&mut self) -> Result<crate::types::MemoryData> {
        if !self.config.monitoring.enable_memory {
            debug!("内存监控已禁用");
            return Ok(crate::types::MemoryData {
                total: 0,
                used: 0,
                available: 0,
                usage_percent: 0.0,
                swap_total: 0,
                swap_used: 0,
                swap_usage_percent: 0.0,
            });
        }

        self.memory_collector.collect().await
    }

    /// 收集磁盘数据
    async fn collect_disk_data(&mut self) -> Result<Vec<crate::types::DiskData>> {
        if !self.config.monitoring.enable_disk {
            debug!("磁盘监控已禁用");
            return Ok(vec![]);
        }

        self.disk_collector.collect().await
    }

    /// 收集网络数据
    async fn collect_network_data(&mut self) -> Result<crate::types::NetworkData> {
        if !self.config.monitoring.enable_network {
            debug!("网络监控已禁用");
            return Ok(crate::types::NetworkData {
                interfaces: vec![],
                connectivity: crate::types::NetworkConnectivity {
                    gateway_reachable: false,
                    dns_working: false,
                    internet_access: false,
                    gateway_ip: None,
                    dns_servers: vec![],
                },
                services: crate::types::NetworkServices {
                    ssh_status: crate::types::ServiceStatus::Unknown,
                    ntp_status: crate::types::ServiceStatus::Unknown,
                    dns_status: crate::types::ServiceStatus::Unknown,
                },
            });
        }

        self.network_collector.collect().await
    }

    /// 收集温度数据
    async fn collect_temperature_data(&mut self) -> Result<Vec<crate::types::TemperatureData>> {
        if !self.config.monitoring.enable_temperature {
            debug!("温度监控已禁用");
            return Ok(vec![]);
        }

        self.temperature_collector.collect().await
    }

    /// 收集存储健康数据
    async fn collect_storage_health_data(&mut self) -> Result<crate::types::StorageHealthData> {
        if !self.config.monitoring.enable_storage_health {
            debug!("存储健康监控已禁用");
            return Ok(crate::types::StorageHealthData {
                devices: vec![],
            });
        }

        self.storage_health_collector.collect().await
    }

    /// 收集文件系统数据
    async fn collect_filesystem_data(&mut self) -> Result<()> {
        if !self.config.monitoring.enable_filesystem {
            debug!("文件系统监控已禁用");
            return Ok(());
        }

        self.filesystem_collector.collect().await
    }

    /// 更新配置
    pub fn update_config(&mut self, config: MonitorConfig) -> Result<()> {
        debug!("更新收集器配置");
        
        // 更新网络收集器的检查主机
        self.network_collector.update_check_hosts(&config.monitoring.network_check_hosts)?;
        
        // 更新文件系统收集器的监控路径
        self.filesystem_collector.update_monit_paths(&config.monitoring.filesystem_paths)?;
        
        self.config = config;
        Ok(())
    }
}

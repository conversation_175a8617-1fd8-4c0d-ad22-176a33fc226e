use anyhow::{Result, Context};
use chrono::{DateTime, Utc};
use log::{debug, warn};
use std::path::{Path, PathBuf};
use async_fs;
use futures_util::stream::StreamExt;
use crate::types::FilesystemData;
use notify::{Config, Event, EventKind, RecursiveMode, Watcher};
use tokio::sync::mpsc::channel;
/// 文件系统信息收集器
pub struct FilesystemCollector {
    watcher: notify::RecommendedWatcher,
    async_event_rx: tokio::sync::mpsc::Receiver<notify::Result<Event>>,
    monit_paths: Vec<PathBuf>,
}

impl FilesystemCollector {
    /// 创建新的文件系统收集器
    pub fn new<P: AsRef<Path>>(paths: &[P]) -> Result<Self> {
        debug!("初始化文件系统收集器");
        let (tx, rx) = channel::<notify::Result<Event>>(1024);
        let mut watcher = notify::recommended_watcher(move |res: std::result::Result<Event, notify::Error>| {
                let tx = tx.clone();
                futures::executor::block_on(async move {
                    if let Ok(event) = &res {
                        if !matches!(event.kind, EventKind::Access(_)) {
                            log::debug!("文件系统变化: {:?}", event);
                        }
                    } 
                    tx.send(res).await.unwrap();
                });
            })?;
        let mut monit_paths = vec![];
        for path in paths {
            watcher.watch(path.as_ref(), RecursiveMode::Recursive).map_err(anyhow::Error::from)?;
            monit_paths.push(path.as_ref().to_path_buf());
        }
        Ok(Self {
            watcher,
            async_event_rx: rx,
            monit_paths,
        })
    }

    /// 收集文件系统信息
    pub async fn collect(&mut self) -> Result<()> {
        debug!("收集文件系统信息");
        Ok(())
    }

    pub fn update_config(&mut self, config: Config) -> Result<bool> {
        self.watcher.configure(config).map_err(anyhow::Error::from)
    }

    pub fn update_monit_paths<P: AsRef<Path>>(&mut self, paths: &[P]) -> Result<()> {
        let mut path_mut = self.watcher.paths_mut();
        for path in &self.monit_paths {
            path_mut.remove(&path)?;
        }
        for path in paths {
            path_mut.add(path.as_ref(), RecursiveMode::Recursive)?;
        }
        path_mut.commit()?;
        self.monit_paths = paths.iter().map(|p| p.as_ref().to_path_buf()).collect();
        Ok(())
    }

    pub fn add_moint_paths<P: AsRef<Path>>(&mut self, paths: &[P]) -> Result<()> {
        for path in paths {
            self.watcher.watch(path.as_ref(), RecursiveMode::Recursive).map_err(anyhow::Error::from)?
        }
        Ok(())
    }

    pub fn remove_moint_paths<P: AsRef<Path>>(&mut self, paths: &[P]) -> Result<()> {
        for path in paths {
            self.watcher.unwatch(path.as_ref()).map_err(anyhow::Error::from)?
        }
        Ok(())
    }

    /// 获取路径数据
    async fn get_path_data(&self, path: &Path) -> Result<FilesystemData> {
        let metadata = async_fs::metadata(path).await
            .context("无法获取文件元数据")?;
        
        let size_bytes = if metadata.is_file() {
            metadata.len()
        } else if metadata.is_dir() {
            self.calculate_directory_size(path).await?
        } else {
            0
        };
        
        let last_modified = metadata.modified()
            .context("无法获取修改时间")?;
        
        let last_modified_utc = DateTime::<Utc>::from(last_modified);
        
        Ok(FilesystemData {
            path: path.to_string_lossy().to_string(),
            size_bytes,
            is_directory: metadata.is_dir(),
            last_modified: last_modified_utc,
        })
    }
    
    /// 收集目录内容
    async fn collect_directory_contents(&self, dir_path: &Path) -> Result<Vec<FilesystemData>> {
        let mut data = Vec::new();

        let mut entries = async_fs::read_dir(dir_path).await
            .context("无法读取目录内容")?;

        while let Some(entry_result) = entries.next().await {
            let entry = entry_result?;
            let entry_path = entry.path();

            // 跳过隐藏文件和特殊目录
            if let Some(file_name) = entry_path.file_name() {
                let file_name_str = file_name.to_string_lossy();
                if file_name_str.starts_with('.') {
                    continue;
                }
            }

            match self.get_path_data(&entry_path).await {
                Ok(entry_data) => {
                    data.push(entry_data);
                },
                Err(e) => {
                    warn!("获取{}信息失败: {}", entry_path.display(), e);
                }
            }
        }

        Ok(data)
    }
    
    /// 计算目录大小
    async fn calculate_directory_size(&self, dir_path: &Path) -> Result<u64> {
        let mut total_size = 0u64;

        let mut entries = async_fs::read_dir(dir_path).await
            .context("无法读取目录内容")?;

        while let Some(entry_result) = entries.next().await {
            let entry = entry_result?;
            let entry_path = entry.path();
            let metadata = entry.metadata().await?;

            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                // 递归计算子目录大小
                match Box::pin(self.calculate_directory_size(&entry_path)).await {
                    Ok(subdir_size) => {
                        total_size += subdir_size;
                    },
                    Err(e) => {
                        warn!("计算子目录{}大小失败: {}", entry_path.display(), e);
                    }
                }
            }
        }

        Ok(total_size)
    }
    
    /// 格式化文件大小
    pub fn format_size(size_bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = size_bytes as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        if unit_index == 0 {
            format!("{} {}", size_bytes, UNITS[unit_index])
        } else {
            format!("{:.2} {}", size, UNITS[unit_index])
        }
    }
    
    /// 检查文件是否在指定时间内被修改
    pub fn is_recently_modified(&self, file_data: &FilesystemData, hours: i64) -> bool {
        let now = Utc::now();
        let threshold = now - chrono::Duration::hours(hours);
        
        file_data.last_modified > threshold
    }
    
    /// 获取最大的文件
    pub fn get_largest_files<'a>(&self, files: &'a [FilesystemData], count: usize) -> Vec<&'a FilesystemData> {
        let mut sorted_files: Vec<&FilesystemData> = files.iter()
            .filter(|f| !f.is_directory)
            .collect();
        
        sorted_files.sort_by(|a, b| b.size_bytes.cmp(&a.size_bytes));
        sorted_files.into_iter().take(count).collect()
    }
    
    /// 获取最大的目录
    pub fn get_largest_directories<'a>(&self, files: &'a [FilesystemData], count: usize) -> Vec<&'a FilesystemData> {
        let mut sorted_dirs: Vec<&FilesystemData> = files.iter()
            .filter(|f| f.is_directory)
            .collect();
        
        sorted_dirs.sort_by(|a, b| b.size_bytes.cmp(&a.size_bytes));
        sorted_dirs.into_iter().take(count).collect()
    }
}

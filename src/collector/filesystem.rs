use anyhow::{Result, Context};
use chrono::{DateTime, Utc};
use log::{debug, warn};
use std::path::Path;
use std::time::SystemTime;
use async_fs;
use futures_core::stream::Stream;
use crate::types::FilesystemData;

/// 文件系统信息收集器
pub struct FilesystemCollector {
    monitor_paths: Vec<String>,
}

impl FilesystemCollector {
    /// 创建新的文件系统收集器
    pub fn new(monitor_paths: &[String]) -> Result<Self> {
        debug!("初始化文件系统收集器");
        
        Ok(Self {
            monitor_paths: monitor_paths.to_vec(),
        })
    }
    
    /// 收集文件系统信息
    pub async fn collect(&mut self) -> Result<Vec<FilesystemData>> {
        debug!("收集文件系统信息");
        
        let mut filesystem_data = Vec::new();
        
        for path in &self.monitor_paths {
            match self.collect_path_info(path).await {
                Ok(mut path_data) => {
                    filesystem_data.append(&mut path_data);
                },
                Err(e) => {
                    warn!("收集路径{}信息失败: {}", path, e);
                }
            }
        }
        
        Ok(filesystem_data)
    }
    
    /// 收集单个路径的信息
    async fn collect_path_info(&self, path: &str) -> Result<Vec<FilesystemData>> {
        let mut data = Vec::new();
        let path_obj = Path::new(path);
        
        if !path_obj.exists() {
            warn!("路径不存在: {}", path);
            return Ok(data);
        }
        
        // 收集当前路径信息
        if let Ok(path_data) = self.get_path_data(path_obj).await {
            data.push(path_data);
        }
        
        // 如果是目录，递归收集子项信息
        if path_obj.is_dir() {
            match self.collect_directory_contents(path_obj).await {
                Ok(mut dir_data) => {
                    data.append(&mut dir_data);
                },
                Err(e) => {
                    warn!("收集目录{}内容失败: {}", path, e);
                }
            }
        }
        
        Ok(data)
    }
    
    /// 获取路径数据
    async fn get_path_data(&self, path: &Path) -> Result<FilesystemData> {
        let metadata = async_fs::metadata(path).await
            .context("无法获取文件元数据")?;
        
        let size_bytes = if metadata.is_file() {
            metadata.len()
        } else if metadata.is_dir() {
            self.calculate_directory_size(path).await?
        } else {
            0
        };
        
        let last_modified = metadata.modified()
            .context("无法获取修改时间")?;
        
        let last_modified_utc = DateTime::<Utc>::from(last_modified);
        
        Ok(FilesystemData {
            path: path.to_string_lossy().to_string(),
            size_bytes,
            is_directory: metadata.is_dir(),
            last_modified: last_modified_utc,
        })
    }
    
    /// 收集目录内容
    async fn collect_directory_contents(&self, dir_path: &Path) -> Result<Vec<FilesystemData>> {
        let mut data = Vec::new();
        
        let entries = async_fs::read_dir(dir_path).await
            .context("无法读取目录内容")?;
        
        for entry in entries.poll_next().await {
            let entry = entry?;
            let entry_path = entry.path();
            
            // 跳过隐藏文件和特殊目录
            if let Some(file_name) = entry_path.file_name() {
                let file_name_str = file_name.to_string_lossy();
                if file_name_str.starts_with('.') {
                    continue;
                }
            }
            
            match self.get_path_data(&entry_path).await {
                Ok(entry_data) => {
                    data.push(entry_data);
                },
                Err(e) => {
                    warn!("获取{}信息失败: {}", entry_path.display(), e);
                }
            }
        }
        
        Ok(data)
    }
    
    /// 计算目录大小
    async fn calculate_directory_size(&self, dir_path: &Path) -> Result<u64> {
        let mut total_size = 0u64;
        
        let entries = async_fs::read_dir(dir_path).await
            .context("无法读取目录内容")?;
        
        for entry in entries.poll_next().await {
            let entry = entry?;
            let entry_path = entry.path();
            let metadata = entry.metadata()?;
            
            if metadata.is_file() {
                total_size += metadata.len();
            } else if metadata.is_dir() {
                // 递归计算子目录大小
                match self.calculate_directory_size(&entry_path).await {
                    Ok(subdir_size) => {
                        total_size += subdir_size;
                    },
                    Err(e) => {
                        warn!("计算子目录{}大小失败: {}", entry_path.display(), e);
                    }
                }
            }
        }
        
        Ok(total_size)
    }
    
    /// 更新监控路径
    pub fn update_paths(&mut self, paths: &[String]) -> Result<()> {
        debug!("更新文件系统监控路径");
        self.monitor_paths = paths.to_vec();
        Ok(())
    }
    
    /// 格式化文件大小
    pub fn format_size(size_bytes: u64) -> String {
        const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
        let mut size = size_bytes as f64;
        let mut unit_index = 0;
        
        while size >= 1024.0 && unit_index < UNITS.len() - 1 {
            size /= 1024.0;
            unit_index += 1;
        }
        
        if unit_index == 0 {
            format!("{} {}", size_bytes, UNITS[unit_index])
        } else {
            format!("{:.2} {}", size, UNITS[unit_index])
        }
    }
    
    /// 检查文件是否在指定时间内被修改
    pub fn is_recently_modified(&self, file_data: &FilesystemData, hours: i64) -> bool {
        let now = Utc::now();
        let threshold = now - chrono::Duration::hours(hours);
        
        file_data.last_modified > threshold
    }
    
    /// 获取最大的文件
    pub fn get_largest_files(&self, files: &[FilesystemData], count: usize) -> Vec<&FilesystemData> {
        let mut sorted_files: Vec<&FilesystemData> = files.iter()
            .filter(|f| !f.is_directory)
            .collect();
        
        sorted_files.sort_by(|a, b| b.size_bytes.cmp(&a.size_bytes));
        sorted_files.into_iter().take(count).collect()
    }
    
    /// 获取最大的目录
    pub fn get_largest_directories(&self, files: &[FilesystemData], count: usize) -> Vec<&FilesystemData> {
        let mut sorted_dirs: Vec<&FilesystemData> = files.iter()
            .filter(|f| f.is_directory)
            .collect();
        
        sorted_dirs.sort_by(|a, b| b.size_bytes.cmp(&a.size_bytes));
        sorted_dirs.into_iter().take(count).collect()
    }
}

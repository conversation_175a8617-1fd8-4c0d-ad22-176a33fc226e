use anyhow::{Result, Context};
use log::{debug, warn};
use procfs::Meminfo;
use sysinfo::{System, SystemExt};

use crate::types::MemoryData;

/// 内存信息收集器
pub struct MemoryCollector {
    system: System,
}

impl MemoryCollector {
    /// 创建新的内存收集器
    pub fn new() -> Result<Self> {
        debug!("初始化内存收集器");
        let mut system = System::new_all();
        system.refresh_memory();
        
        Ok(Self { system })
    }
    
    /// 收集内存信息
    pub async fn collect(&mut self) -> Result<MemoryData> {
        debug!("收集内存信息");
        
        // 刷新内存信息
        self.system.refresh_memory();
        
        // 尝试从procfs获取详细内存信息
        match self.collect_from_procfs() {
            Ok(memory_data) => Ok(memory_data),
            Err(e) => {
                warn!("从procfs获取内存信息失败，使用sysinfo: {}", e);
                self.collect_from_sysinfo()
            }
        }
    }
    
    /// 从procfs收集内存信息
    fn collect_from_procfs(&self) -> Result<MemoryData> {
        let meminfo = Meminfo::new()
            .context("无法读取/proc/meminfo")?;
        
        let total = meminfo.mem_total * 1024; // 转换为字节
        let available = meminfo.mem_available.unwrap_or(0) * 1024;
        let free = meminfo.mem_free * 1024;
        let buffers = meminfo.buffers.unwrap_or(0) * 1024;
        let cached = meminfo.cached.unwrap_or(0) * 1024;
        
        // 计算实际可用内存
        let actual_available = if available > 0 {
            available
        } else {
            free + buffers + cached
        };
        
        let used = total - actual_available;
        let usage_percent = if total > 0 {
            (used as f64 / total as f64) * 100.0
        } else {
            0.0
        };
        
        // 交换分区信息
        let swap_total = meminfo.swap_total * 1024;
        let swap_free = meminfo.swap_free * 1024;
        let swap_used = swap_total - swap_free;
        let swap_usage_percent = if swap_total > 0 {
            (swap_used as f64 / swap_total as f64) * 100.0
        } else {
            0.0
        };
        
        Ok(MemoryData {
            total,
            used,
            available: actual_available,
            usage_percent,
            swap_total,
            swap_used,
            swap_usage_percent,
        })
    }
    
    /// 从sysinfo收集内存信息
    fn collect_from_sysinfo(&self) -> Result<MemoryData> {
        let total = self.system.total_memory() * 1024; // sysinfo返回KB，转换为字节
        let used = self.system.used_memory() * 1024;
        let available = self.system.available_memory() * 1024;
        
        let usage_percent = if total > 0 {
            (used as f64 / total as f64) * 100.0
        } else {
            0.0
        };
        
        let swap_total = self.system.total_swap() * 1024;
        let swap_used = self.system.used_swap() * 1024;
        let swap_usage_percent = if swap_total > 0 {
            (swap_used as f64 / swap_total as f64) * 100.0
        } else {
            0.0
        };
        
        Ok(MemoryData {
            total,
            used,
            available,
            usage_percent,
            swap_total,
            swap_used,
            swap_usage_percent,
        })
    }
}

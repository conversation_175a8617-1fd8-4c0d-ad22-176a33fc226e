use anyhow::{Result, Context};
use log::{debug, warn};
use std::fs;
use std::path::Path;
use std::process::Command;
use std::collections::HashMap;

use crate::types::{StorageHealthData, StorageDevice, StorageType};

/// 存储健康信息收集器
pub struct StorageHealthCollector {
    sector_counts: HashMap<String, u64>,
}

impl StorageHealthCollector {
    /// 创建新的存储健康收集器
    pub fn new() -> Result<Self> {
        debug!("初始化存储健康收集器");
        
        Ok(Self {
            sector_counts: HashMap::new(),
        })
    }
    
    /// 收集存储健康信息
    pub async fn collect(&mut self) -> Result<StorageHealthData> {
        debug!("收集存储健康信息");
        
        let mut devices = Vec::new();
        
        // 获取所有块设备
        let block_devices = self.get_block_devices()?;
        
        for device in block_devices {
            if let Ok(storage_device) = self.collect_device_info(&device).await {
                devices.push(storage_device);
            }
        }
        
        Ok(StorageHealthData { devices })
    }
    
    fn forlinx_tool<P: AsRef<OsStr>>(&self, devices: &[P]) -> Vec<StorageDevice> {
        for path in devices {
            let cmd = std::process::Command::new("mmcinfo").args(vec![path, "-y", "5", "-debug"])
                .spawn();

        }
    }
}

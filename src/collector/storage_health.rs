use anyhow::{Result, Context};
use log::{debug, warn};
use std::fs;
use std::path::Path;
use std::process::Command;
use std::collections::HashMap;

use crate::types::{StorageHealthData, StorageDevice, StorageType};

/// 存储健康信息收集器
pub struct StorageHealthCollector {
    sector_counts: HashMap<String, u64>,
}

impl StorageHealthCollector {
    /// 创建新的存储健康收集器
    pub fn new() -> Result<Self> {
        debug!("初始化存储健康收集器");
        
        Ok(Self {
            sector_counts: HashMap::new(),
        })
    }
    
    /// 收集存储健康信息
    pub async fn collect(&mut self) -> Result<StorageHealthData> {
        debug!("收集存储健康信息");
        
        let mut devices = Vec::new();
        
        // 获取所有块设备
        let block_devices = self.get_block_devices()?;
        
        for device in block_devices {
            if let Ok(storage_device) = self.collect_device_info(&device).await {
                devices.push(storage_device);
            }
        }
        
        Ok(StorageHealthData { devices })
    }
    
    /// 获取块设备列表
    fn get_block_devices(&self) -> Result<Vec<String>> {
        let mut devices = Vec::new();
        let sys_block_path = Path::new("/sys/block");
        
        if !sys_block_path.exists() {
            return Ok(devices);
        }
        
        for entry in fs::read_dir(sys_block_path)? {
            let entry = entry?;
            let device_name = entry.file_name().to_string_lossy().to_string();
            
            // 过滤掉循环设备、RAM设备等
            if !device_name.starts_with("loop") && 
               !device_name.starts_with("ram") && 
               !device_name.starts_with("dm-") {
                devices.push(device_name);
            }
        }
        
        Ok(devices)
    }
    
    /// 收集单个设备信息
    async fn collect_device_info(&mut self, device: &str) -> Result<StorageDevice> {
        let device_path = format!("/dev/{}", device);
        
        // 获取设备类型
        let device_type = self.detect_storage_type(device)?;
        
        // 获取总扇区数
        let total_sectors = self.get_total_sectors(device)?;
        
        // 获取写入扇区数
        let sectors_written = self.get_sectors_written(device)?;
        
        // 计算写入周期数
        let write_cycles = if total_sectors > 0 {
            sectors_written / total_sectors
        } else {
            0
        };
        
        // 获取健康百分比
        let health_percentage = self.get_health_percentage(device).await?;
        
        // 估算剩余寿命
        let estimated_life_remaining = self.estimate_life_remaining(device, &device_type, write_cycles)?;
        
        // 更新扇区计数
        self.sector_counts.insert(device.to_string(), sectors_written);
        
        Ok(StorageDevice {
            device: device_path,
            device_type,
            total_sectors,
            sectors_written,
            write_cycles,
            health_percentage,
            estimated_life_remaining,
        })
    }
    
    /// 检测存储类型
    fn detect_storage_type(&self, device: &str) -> Result<StorageType> {
        // 检查是否为SSD
        let rotational_file = format!("/sys/block/{}/queue/rotational", device);
        if let Ok(content) = fs::read_to_string(&rotational_file) {
            if content.trim() == "0" {
                // 进一步区分SSD类型
                if device.starts_with("mmcblk") {
                    return Ok(StorageType::eMMC);
                } else if device.contains("sd") && self.is_sd_card(device)? {
                    return Ok(StorageType::SDCard);
                } else {
                    return Ok(StorageType::SSD);
                }
            } else {
                return Ok(StorageType::HDD);
            }
        }
        
        // 根据设备名称推断
        if device.starts_with("mmcblk") {
            Ok(StorageType::eMMC)
        } else if device.starts_with("nvme") {
            Ok(StorageType::SSD)
        } else if device.starts_with("sd") {
            // 可能是SATA SSD或HDD，需要进一步检查
            Ok(StorageType::Unknown)
        } else {
            Ok(StorageType::Unknown)
        }
    }
    
    /// 检查是否为SD卡
    fn is_sd_card(&self, device: &str) -> Result<bool> {
        // 检查设备是否通过USB连接
        let device_path = format!("/sys/block/{}", device);
        let real_path = fs::read_link(&device_path)?;
        let path_str = real_path.to_string_lossy();
        
        Ok(path_str.contains("usb") || path_str.contains("mmc"))
    }
    
    /// 获取总扇区数
    fn get_total_sectors(&self, device: &str) -> Result<u64> {
        let size_file = format!("/sys/block/{}/size", device);
        let content = fs::read_to_string(&size_file)
            .context("无法读取设备大小")?;
        
        content.trim().parse::<u64>()
            .context("无法解析设备大小")
    }
    
    /// 获取写入扇区数
    fn get_sectors_written(&self, device: &str) -> Result<u64> {
        let stat_file = format!("/sys/block/{}/stat", device);
        let content = fs::read_to_string(&stat_file)
            .context("无法读取设备统计信息")?;
        
        let fields: Vec<&str> = content.split_whitespace().collect();
        if fields.len() >= 10 {
            // 第7个字段是写入扇区数
            fields[6].parse::<u64>()
                .context("无法解析写入扇区数")
        } else {
            Ok(0)
        }
    }
    
    /// 获取健康百分比
    async fn get_health_percentage(&self, device: &str) -> Result<Option<f64>> {
        let device_path = format!("/dev/{}", device);
        
        // 尝试使用smartctl获取SMART信息
        let output = tokio::process::Command::new("smartctl")
            .args(&["-A", &device_path])
            .output()
            .await;
        
        match output {
            Ok(output) => {
                let output_str = String::from_utf8_lossy(&output.stdout);
                
                // 查找健康相关的属性
                for line in output_str.lines() {
                    if line.contains("Wear_Leveling_Count") || 
                       line.contains("SSD_Life_Left") ||
                       line.contains("Percent_Lifetime_Remain") {
                        let fields: Vec<&str> = line.split_whitespace().collect();
                        if fields.len() >= 10 {
                            if let Ok(value) = fields[9].parse::<f64>() {
                                return Ok(Some(value));
                            }
                        }
                    }
                }
                
                // 如果没有找到特定属性，尝试获取总体健康状态
                if output_str.contains("PASSED") {
                    Ok(Some(100.0))
                } else if output_str.contains("FAILED") {
                    Ok(Some(0.0))
                } else {
                    Ok(None)
                }
            },
            Err(_) => {
                warn!("无法获取设备{}的SMART信息", device);
                Ok(None)
            }
        }
    }
    
    /// 估算剩余寿命
    fn estimate_life_remaining(
        &self, 
        device: &str, 
        device_type: &StorageType, 
        write_cycles: u64
    ) -> Result<Option<u64>> {
        match device_type {
            StorageType::SSD => {
                // SSD通常可以承受1000-10000次写入周期
                let max_cycles = 3000; // 保守估计
                if write_cycles < max_cycles {
                    let remaining_cycles = max_cycles - write_cycles;
                    // 假设每天写入一次完整容量
                    Ok(Some(remaining_cycles * 365))
                } else {
                    Ok(Some(0))
                }
            },
            StorageType::eMMC => {
                // eMMC通常可以承受500-3000次写入周期
                let max_cycles = 1000;
                if write_cycles < max_cycles {
                    let remaining_cycles = max_cycles - write_cycles;
                    Ok(Some(remaining_cycles * 365))
                } else {
                    Ok(Some(0))
                }
            },
            StorageType::SDCard => {
                // SD卡通常可以承受100-1000次写入周期
                let max_cycles = 500;
                if write_cycles < max_cycles {
                    let remaining_cycles = max_cycles - write_cycles;
                    Ok(Some(remaining_cycles * 365))
                } else {
                    Ok(Some(0))
                }
            },
            StorageType::HDD => {
                // HDD的寿命主要取决于运行时间而不是写入周期
                Ok(None)
            },
            StorageType::Unknown => Ok(None),
        }
    }
}

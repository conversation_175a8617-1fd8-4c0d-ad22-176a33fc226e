use anyhow::Result;
use chrono::Utc;
use log::{info, debug};
use std::collections::HashMap;
use uuid::Uuid;

use crate::types::{
    SystemData, MonitorConfig, Alert, AlertType, AlertSeverity,
    ServiceStatus
};

/// 预警系统
pub struct AlertSystem {
    config: MonitorConfig,
    active_alerts: HashMap<String, Alert>,
    alert_history: Vec<Alert>,
}

impl AlertSystem {
    /// 创建新的预警系统
    pub fn new(config: &MonitorConfig) -> Result<Self> {
        info!("初始化预警系统");
        
        Ok(Self {
            config: config.clone(),
            active_alerts: HashMap::new(),
            alert_history: Vec::new(),
        })
    }
    
    /// 检查预警条件
    pub async fn check_alerts(&mut self, data: &SystemData) -> Result<Option<Vec<Alert>>> {
        debug!("检查预警条件");
        
        let mut new_alerts = Vec::new();
        
        // 检查CPU使用率
        if let Some(alert) = self.check_cpu_usage(data)? {
            new_alerts.push(alert);
        }
        
        // 检查内存使用率
        if let Some(alert) = self.check_memory_usage(data)? {
            new_alerts.push(alert);
        }
        
        // 检查磁盘使用率
        new_alerts.extend(self.check_disk_usage(data)?);
        
        // 检查温度
        new_alerts.extend(self.check_temperature(data)?);
        
        // 检查存储健康
        new_alerts.extend(self.check_storage_health(data)?);
        
        // 检查网络连通性
        if let Some(alert) = self.check_network_connectivity(data)? {
            new_alerts.push(alert);
        }
        
        // 检查服务状态
        new_alerts.extend(self.check_service_status(data)?);
        
        // 更新活跃预警列表
        for alert in &new_alerts {
            self.active_alerts.insert(alert.id.clone(), alert.clone());
            self.alert_history.push(alert.clone());
        }
        
        // 清理已解决的预警
        self.cleanup_resolved_alerts(data)?;
        
        if new_alerts.is_empty() {
            Ok(None)
        } else {
            info!("生成了{}个新预警", new_alerts.len());
            Ok(Some(new_alerts))
        }
    }
    
    /// 检查CPU使用率
    fn check_cpu_usage(&self, data: &SystemData) -> Result<Option<Alert>> {
        let usage = data.cpu.usage_percent;
        let thresholds = &self.config.thresholds;
        
        if usage >= thresholds.cpu_usage_critical {
            Ok(Some(self.create_alert(
                AlertType::CpuUsage,
                AlertSeverity::Critical,
                format!("CPU使用率过高: {:.1}%", usage),
                Some(usage),
                Some(thresholds.cpu_usage_critical),
            )))
        } else if usage >= thresholds.cpu_usage_warning {
            Ok(Some(self.create_alert(
                AlertType::CpuUsage,
                AlertSeverity::Warning,
                format!("CPU使用率较高: {:.1}%", usage),
                Some(usage),
                Some(thresholds.cpu_usage_warning),
            )))
        } else {
            Ok(None)
        }
    }
    
    /// 检查内存使用率
    fn check_memory_usage(&self, data: &SystemData) -> Result<Option<Alert>> {
        let usage = data.memory.usage_percent;
        let thresholds = &self.config.thresholds;
        
        if usage >= thresholds.memory_usage_critical {
            Ok(Some(self.create_alert(
                AlertType::MemoryUsage,
                AlertSeverity::Critical,
                format!("内存使用率过高: {:.1}%", usage),
                Some(usage),
                Some(thresholds.memory_usage_critical),
            )))
        } else if usage >= thresholds.memory_usage_warning {
            Ok(Some(self.create_alert(
                AlertType::MemoryUsage,
                AlertSeverity::Warning,
                format!("内存使用率较高: {:.1}%", usage),
                Some(usage),
                Some(thresholds.memory_usage_warning),
            )))
        } else {
            Ok(None)
        }
    }
    
    /// 检查磁盘使用率
    fn check_disk_usage(&self, data: &SystemData) -> Result<Vec<Alert>> {
        let mut alerts = Vec::new();
        let thresholds = &self.config.thresholds;
        
        for disk in &data.disk {
            // 检查磁盘空间使用率
            if disk.usage_percent >= thresholds.disk_usage_critical {
                alerts.push(self.create_alert(
                    AlertType::DiskUsage,
                    AlertSeverity::Critical,
                    format!("磁盘{}空间不足: {:.1}%", disk.device, disk.usage_percent),
                    Some(disk.usage_percent),
                    Some(thresholds.disk_usage_critical),
                ));
            } else if disk.usage_percent >= thresholds.disk_usage_warning {
                alerts.push(self.create_alert(
                    AlertType::DiskUsage,
                    AlertSeverity::Warning,
                    format!("磁盘{}空间较少: {:.1}%", disk.device, disk.usage_percent),
                    Some(disk.usage_percent),
                    Some(thresholds.disk_usage_warning),
                ));
            }
            
            // 检查inode使用率
            if let Some(inode_usage) = disk.inodes_usage_percent {
                if inode_usage >= thresholds.inode_usage_critical {
                    alerts.push(self.create_alert(
                        AlertType::DiskInodeUsage,
                        AlertSeverity::Critical,
                        format!("磁盘{}inode不足: {:.1}%", disk.device, inode_usage),
                        Some(inode_usage),
                        Some(thresholds.inode_usage_critical),
                    ));
                } else if inode_usage >= thresholds.inode_usage_warning {
                    alerts.push(self.create_alert(
                        AlertType::DiskInodeUsage,
                        AlertSeverity::Warning,
                        format!("磁盘{}inode较少: {:.1}%", disk.device, inode_usage),
                        Some(inode_usage),
                        Some(thresholds.inode_usage_warning),
                    ));
                }
            }
        }
        
        Ok(alerts)
    }
    
    /// 检查温度
    fn check_temperature(&self, data: &SystemData) -> Result<Vec<Alert>> {
        let mut alerts = Vec::new();
        let thresholds = &self.config.thresholds;
        
        for temp_data in &data.temperature {
            let temp = temp_data.temperature;
            
            // 使用传感器自身的临界温度，如果没有则使用配置的阈值
            let critical_threshold = temp_data.critical_temp
                .unwrap_or(thresholds.temperature_critical);
            let warning_threshold = thresholds.temperature_warning;
            
            if temp >= critical_threshold {
                alerts.push(self.create_alert(
                    AlertType::Temperature,
                    AlertSeverity::Critical,
                    format!("传感器{}温度过高: {:.1}°C", temp_data.sensor_name, temp),
                    Some(temp),
                    Some(critical_threshold),
                ));
            } else if temp >= warning_threshold {
                alerts.push(self.create_alert(
                    AlertType::Temperature,
                    AlertSeverity::Warning,
                    format!("传感器{}温度较高: {:.1}°C", temp_data.sensor_name, temp),
                    Some(temp),
                    Some(warning_threshold),
                ));
            }
        }
        
        Ok(alerts)
    }
    
    /// 检查存储健康
    fn check_storage_health(&self, data: &SystemData) -> Result<Vec<Alert>> {
        let mut alerts = Vec::new();
        let thresholds = &self.config.thresholds;
        
        for device in &data.storage_health.devices {
            if let Some(health) = device.health_percentage {
                if health <= thresholds.storage_health_critical {
                    alerts.push(self.create_alert(
                        AlertType::StorageHealth,
                        AlertSeverity::Critical,
                        format!("存储设备{}健康状况严重: {:.1}%", device.device, health),
                        Some(health),
                        Some(thresholds.storage_health_critical),
                    ));
                } else if health <= thresholds.storage_health_warning {
                    alerts.push(self.create_alert(
                        AlertType::StorageHealth,
                        AlertSeverity::Warning,
                        format!("存储设备{}健康状况不佳: {:.1}%", device.device, health),
                        Some(health),
                        Some(thresholds.storage_health_warning),
                    ));
                }
            }
            
            // 检查预估剩余寿命
            if let Some(life_remaining) = device.estimated_life_remaining {
                if life_remaining <= 30 {
                    alerts.push(self.create_alert(
                        AlertType::StorageHealth,
                        AlertSeverity::Critical,
                        format!("存储设备{}预估剩余寿命不足: {}天", device.device, life_remaining),
                        Some(life_remaining as f64),
                        Some(30.0),
                    ));
                } else if life_remaining <= 90 {
                    alerts.push(self.create_alert(
                        AlertType::StorageHealth,
                        AlertSeverity::Warning,
                        format!("存储设备{}预估剩余寿命较短: {}天", device.device, life_remaining),
                        Some(life_remaining as f64),
                        Some(90.0),
                    ));
                }
            }
        }
        
        Ok(alerts)
    }
    
    /// 检查网络连通性
    fn check_network_connectivity(&self, data: &SystemData) -> Result<Option<Alert>> {
        let connectivity = &data.network.connectivity;
        
        if !connectivity.internet_access {
            Ok(Some(self.create_alert(
                AlertType::NetworkConnectivity,
                AlertSeverity::Critical,
                "互联网连接中断".to_string(),
                None,
                None,
            )))
        } else if !connectivity.gateway_reachable {
            Ok(Some(self.create_alert(
                AlertType::NetworkConnectivity,
                AlertSeverity::Warning,
                "网关不可达".to_string(),
                None,
                None,
            )))
        } else if !connectivity.dns_working {
            Ok(Some(self.create_alert(
                AlertType::NetworkConnectivity,
                AlertSeverity::Warning,
                "DNS解析失败".to_string(),
                None,
                None,
            )))
        } else {
            Ok(None)
        }
    }
    
    /// 检查服务状态
    fn check_service_status(&self, data: &SystemData) -> Result<Vec<Alert>> {
        let mut alerts = Vec::new();
        let services = &data.network.services;
        
        // 检查SSH服务
        if matches!(services.ssh_status, ServiceStatus::Failed | ServiceStatus::Stopped) {
            alerts.push(self.create_alert(
                AlertType::ServiceDown,
                AlertSeverity::Warning,
                format!("SSH服务异常: {:?}", services.ssh_status),
                None,
                None,
            ));
        }
        
        // 检查NTP服务
        if matches!(services.ntp_status, ServiceStatus::Failed | ServiceStatus::Stopped) {
            alerts.push(self.create_alert(
                AlertType::ServiceDown,
                AlertSeverity::Warning,
                format!("NTP服务异常: {:?}", services.ntp_status),
                None,
                None,
            ));
        }
        
        // 检查DNS服务
        if matches!(services.dns_status, ServiceStatus::Failed | ServiceStatus::Stopped) {
            alerts.push(self.create_alert(
                AlertType::ServiceDown,
                AlertSeverity::Warning,
                format!("DNS服务异常: {:?}", services.dns_status),
                None,
                None,
            ));
        }
        
        Ok(alerts)
    }
    
    /// 创建预警
    fn create_alert(
        &self,
        alert_type: AlertType,
        severity: AlertSeverity,
        message: String,
        value: Option<f64>,
        threshold: Option<f64>,
    ) -> Alert {
        Alert {
            id: Uuid::new_v4().to_string(),
            alert_type,
            severity,
            message,
            timestamp: Utc::now(),
            value,
            threshold,
        }
    }
    
    /// 清理已解决的预警
    fn cleanup_resolved_alerts(&mut self, data: &SystemData) -> Result<()> {
        let mut resolved_alerts = Vec::new();
        
        for (alert_id, alert) in &self.active_alerts {
            let is_resolved = match alert.alert_type {
                AlertType::CpuUsage => {
                    data.cpu.usage_percent < self.config.thresholds.cpu_usage_warning
                },
                AlertType::MemoryUsage => {
                    data.memory.usage_percent < self.config.thresholds.memory_usage_warning
                },
                AlertType::NetworkConnectivity => {
                    data.network.connectivity.internet_access
                },
                _ => false, // 其他类型的预警需要手动解决
            };
            
            if is_resolved {
                resolved_alerts.push(alert_id.clone());
            }
        }
        
        for alert_id in resolved_alerts {
            self.active_alerts.remove(&alert_id);
            debug!("预警已自动解决: {}", alert_id);
        }
        
        Ok(())
    }
    
    /// 获取活跃预警
    pub fn get_active_alerts(&self) -> Vec<&Alert> {
        self.active_alerts.values().collect()
    }
    
    /// 手动解决预警
    pub fn resolve_alert(&mut self, alert_id: &str) -> Result<()> {
        if self.active_alerts.remove(alert_id).is_some() {
            info!("预警已手动解决: {}", alert_id);
            Ok(())
        } else {
            Err(anyhow::anyhow!("预警不存在: {}", alert_id))
        }
    }
    
    /// 更新配置
    pub fn update_config(&mut self, config: MonitorConfig) {
        debug!("更新预警系统配置");
        self.config = config;
    }
}

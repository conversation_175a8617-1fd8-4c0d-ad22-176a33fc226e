use anyhow::{Result, Context};
use chrono::{DateTime, Utc, Duration};
use log::{info, warn, debug};
use rusqlite::{Connection, params};
use std::path::Path;
use std::sync::{Arc, Mutex};
use tokio::task;

use crate::types::{SystemData, MonitorConfig, Alert};

/// 数据存储管理器
pub struct DataStorage {
    connection: Arc<Mutex<Connection>>,
    config: MonitorConfig,
}

impl DataStorage {
    /// 创建新的数据存储管理器
    pub async fn new(config: &MonitorConfig) -> Result<Self> {
        info!("初始化数据存储管理器");
        
        // 确保数据库目录存在
        let db_path = Path::new(&config.database_path);
        if let Some(parent) = db_path.parent() {
            tokio::fs::create_dir_all(parent).await
                .context("无法创建数据库目录")?;
        }
        
        // 创建数据库连接
        let connection = task::spawn_blocking({
            let db_path = config.database_path.clone();
            move || -> Result<Connection> {
                let conn = Connection::open(&db_path)
                    .context("无法打开数据库")?;
                
                // 启用WAL模式以提高并发性能
                conn.execute_batch("PRAGMA journal_mode=WAL; PRAGMA synchronous=NORMAL;")
                    .context("无法设置数据库模式")?;
                
                Ok(conn)
            }
        }).await??;
        
        let storage = Self {
            connection: Arc::new(Mutex::new(connection)),
            config: config.clone(),
        };
        
        // 初始化数据库表
        storage.initialize_tables().await?;
        
        info!("数据存储管理器初始化完成");
        Ok(storage)
    }
    
    /// 初始化数据库表
    async fn initialize_tables(&self) -> Result<()> {
        debug!("初始化数据库表");
        
        let connection = Arc::clone(&self.connection);
        task::spawn_blocking(move || -> Result<()> {
            let conn = connection.lock().unwrap();
            
            // 系统数据表
            conn.execute(
                "CREATE TABLE IF NOT EXISTS system_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    cpu_usage REAL NOT NULL,
                    cpu_load_1m REAL NOT NULL,
                    cpu_load_5m REAL NOT NULL,
                    cpu_load_15m REAL NOT NULL,
                    memory_total INTEGER NOT NULL,
                    memory_used INTEGER NOT NULL,
                    memory_usage_percent REAL NOT NULL,
                    swap_total INTEGER NOT NULL,
                    swap_used INTEGER NOT NULL,
                    swap_usage_percent REAL NOT NULL,
                    data_json TEXT NOT NULL
                )",
                [],
            ).context("无法创建system_data表")?;
            
            // 磁盘数据表
            conn.execute(
                "CREATE TABLE IF NOT EXISTS disk_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    device TEXT NOT NULL,
                    mount_point TEXT NOT NULL,
                    total_space INTEGER NOT NULL,
                    used_space INTEGER NOT NULL,
                    usage_percent REAL NOT NULL,
                    inodes_total INTEGER,
                    inodes_used INTEGER,
                    inodes_usage_percent REAL,
                    read_bytes_per_sec INTEGER,
                    write_bytes_per_sec INTEGER
                )",
                [],
            ).context("无法创建disk_data表")?;
            
            // 网络数据表
            conn.execute(
                "CREATE TABLE IF NOT EXISTS network_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    interface_name TEXT NOT NULL,
                    ip_address TEXT,
                    is_up INTEGER NOT NULL,
                    bytes_sent INTEGER NOT NULL,
                    bytes_received INTEGER NOT NULL,
                    packets_sent INTEGER NOT NULL,
                    packets_received INTEGER NOT NULL,
                    errors_in INTEGER NOT NULL,
                    errors_out INTEGER NOT NULL
                )",
                [],
            ).context("无法创建network_data表")?;
            
            // 温度数据表
            conn.execute(
                "CREATE TABLE IF NOT EXISTS temperature_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    sensor_name TEXT NOT NULL,
                    temperature REAL NOT NULL,
                    critical_temp REAL,
                    max_temp REAL
                )",
                [],
            ).context("无法创建temperature_data表")?;
            
            // 存储健康数据表
            conn.execute(
                "CREATE TABLE IF NOT EXISTS storage_health_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    device TEXT NOT NULL,
                    device_type TEXT NOT NULL,
                    total_sectors INTEGER NOT NULL,
                    sectors_written INTEGER NOT NULL,
                    write_cycles INTEGER NOT NULL,
                    health_percentage REAL,
                    estimated_life_remaining INTEGER
                )",
                [],
            ).context("无法创建storage_health_data表")?;
            
            // 预警数据表
            conn.execute(
                "CREATE TABLE IF NOT EXISTS alerts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    alert_id TEXT NOT NULL UNIQUE,
                    alert_type TEXT NOT NULL,
                    severity TEXT NOT NULL,
                    message TEXT NOT NULL,
                    timestamp TEXT NOT NULL,
                    value REAL,
                    threshold REAL,
                    resolved INTEGER DEFAULT 0,
                    resolved_timestamp TEXT
                )",
                [],
            ).context("无法创建alerts表")?;
            
            // 创建索引
            conn.execute("CREATE INDEX IF NOT EXISTS idx_system_data_timestamp ON system_data(timestamp)", [])
                .context("无法创建system_data时间戳索引")?;
            
            conn.execute("CREATE INDEX IF NOT EXISTS idx_disk_data_timestamp ON disk_data(timestamp)", [])
                .context("无法创建disk_data时间戳索引")?;
            
            conn.execute("CREATE INDEX IF NOT EXISTS idx_network_data_timestamp ON network_data(timestamp)", [])
                .context("无法创建network_data时间戳索引")?;
            
            conn.execute("CREATE INDEX IF NOT EXISTS idx_temperature_data_timestamp ON temperature_data(timestamp)", [])
                .context("无法创建temperature_data时间戳索引")?;
            
            conn.execute("CREATE INDEX IF NOT EXISTS idx_storage_health_data_timestamp ON storage_health_data(timestamp)", [])
                .context("无法创建storage_health_data时间戳索引")?;
            
            conn.execute("CREATE INDEX IF NOT EXISTS idx_alerts_timestamp ON alerts(timestamp)", [])
                .context("无法创建alerts时间戳索引")?;
            
            Ok(())
        }).await??;
        
        debug!("数据库表初始化完成");
        Ok(())
    }
    
    /// 存储系统数据
    pub async fn store_system_data(&self, data: &SystemData) -> Result<()> {
        debug!("存储系统数据");
        
        let connection = Arc::clone(&self.connection);
        let data_clone = data.clone();
        
        task::spawn_blocking(move || -> Result<()> {
            let conn = connection.lock().unwrap();
            let tx = conn.unchecked_transaction()?;
            
            // 序列化完整数据为JSON
            let data_json = serde_json::to_string(&data_clone)
                .context("无法序列化系统数据")?;
            
            // 存储主要系统数据
            tx.execute(
                "INSERT INTO system_data (
                    timestamp, cpu_usage, cpu_load_1m, cpu_load_5m, cpu_load_15m,
                    memory_total, memory_used, memory_usage_percent,
                    swap_total, swap_used, swap_usage_percent, data_json
                ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11, ?12)",
                params![
                    data_clone.timestamp.to_rfc3339(),
                    data_clone.cpu.usage_percent,
                    data_clone.cpu.load_average.one_minute,
                    data_clone.cpu.load_average.five_minutes,
                    data_clone.cpu.load_average.fifteen_minutes,
                    data_clone.memory.total as i64,
                    data_clone.memory.used as i64,
                    data_clone.memory.usage_percent,
                    data_clone.memory.swap_total as i64,
                    data_clone.memory.swap_used as i64,
                    data_clone.memory.swap_usage_percent,
                    data_json
                ],
            ).context("无法插入系统数据")?;
            
            // 存储磁盘数据
            for disk in &data_clone.disk {
                tx.execute(
                    "INSERT INTO disk_data (
                        timestamp, device, mount_point, total_space, used_space, usage_percent,
                        inodes_total, inodes_used, inodes_usage_percent,
                        read_bytes_per_sec, write_bytes_per_sec
                    ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10, ?11)",
                    params![
                        data_clone.timestamp.to_rfc3339(),
                        disk.device,
                        disk.mount_point,
                        disk.total_space as i64,
                        disk.used_space as i64,
                        disk.usage_percent,
                        disk.inodes_total.map(|v| v as i64),
                        disk.inodes_used.map(|v| v as i64),
                        disk.inodes_usage_percent,
                        disk.read_bytes_per_sec.map(|v| v as i64),
                        disk.write_bytes_per_sec.map(|v| v as i64)
                    ],
                ).context("无法插入磁盘数据")?;
            }
            
            // 存储网络接口数据
            for interface in &data_clone.network.interfaces {
                tx.execute(
                    "INSERT INTO network_data (
                        timestamp, interface_name, ip_address, is_up,
                        bytes_sent, bytes_received, packets_sent, packets_received,
                        errors_in, errors_out
                    ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8, ?9, ?10)",
                    params![
                        data_clone.timestamp.to_rfc3339(),
                        interface.name,
                        interface.ip_address,
                        interface.is_up as i32,
                        interface.bytes_sent as i64,
                        interface.bytes_received as i64,
                        interface.packets_sent as i64,
                        interface.packets_received as i64,
                        interface.errors_in as i64,
                        interface.errors_out as i64
                    ],
                ).context("无法插入网络数据")?;
            }
            
            // 存储温度数据
            for temp in &data_clone.temperature {
                tx.execute(
                    "INSERT INTO temperature_data (
                        timestamp, sensor_name, temperature, critical_temp, max_temp
                    ) VALUES (?1, ?2, ?3, ?4, ?5)",
                    params![
                        data_clone.timestamp.to_rfc3339(),
                        temp.sensor_name,
                        temp.temperature,
                        temp.critical_temp,
                        temp.max_temp
                    ],
                ).context("无法插入温度数据")?;
            }
            
            // 存储存储健康数据
            for device in &data_clone.storage_health.devices {
                tx.execute(
                    "INSERT INTO storage_health_data (
                        timestamp, device, device_type, total_sectors, sectors_written,
                        write_cycles, health_percentage, estimated_life_remaining
                    ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7, ?8)",
                    params![
                        data_clone.timestamp.to_rfc3339(),
                        device.device,
                        format!("{:?}", device.device_type),
                        device.total_sectors as i64,
                        device.sectors_written as i64,
                        device.write_cycles as i64,
                        device.health_percentage,
                        device.estimated_life_remaining.map(|v| v as i64)
                    ],
                ).context("无法插入存储健康数据")?;
            }
            
            tx.commit().context("无法提交事务")?;
            Ok(())
        }).await??;
        
        debug!("系统数据存储完成");
        Ok(())
    }
    
    /// 存储预警数据
    pub async fn store_alert(&self, alert: &Alert) -> Result<()> {
        debug!("存储预警数据: {}", alert.id);
        
        let connection = Arc::clone(&self.connection);
        let alert_clone = alert.clone();
        
        task::spawn_blocking(move || -> Result<()> {
            let conn = connection.lock().unwrap();
            
            conn.execute(
                "INSERT OR REPLACE INTO alerts (
                    alert_id, alert_type, severity, message, timestamp, value, threshold
                ) VALUES (?1, ?2, ?3, ?4, ?5, ?6, ?7)",
                params![
                    alert_clone.id,
                    format!("{:?}", alert_clone.alert_type),
                    format!("{:?}", alert_clone.severity),
                    alert_clone.message,
                    alert_clone.timestamp.to_rfc3339(),
                    alert_clone.value,
                    alert_clone.threshold
                ],
            ).context("无法插入预警数据")?;
            
            Ok(())
        }).await??;
        
        debug!("预警数据存储完成");
        Ok(())
    }

    /// 查询系统数据
    pub async fn query_system_data(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        limit: Option<u32>,
    ) -> Result<Vec<SystemData>> {
        debug!("查询系统数据: {} 到 {}", start_time, end_time);

        let connection = Arc::clone(&self.connection);

        task::spawn_blocking(move || -> Result<Vec<SystemData>> {
            let conn = connection.lock().unwrap();

            let mut results = Vec::new();

            if let Some(limit) = limit {
                let mut stmt = conn.prepare(
                    "SELECT data_json FROM system_data
                     WHERE timestamp BETWEEN ?1 AND ?2
                     ORDER BY timestamp DESC LIMIT ?3"
                )?;
                let rows = stmt.query_map(params![
                    start_time.to_rfc3339(),
                    end_time.to_rfc3339(),
                    limit
                ], |row| {
                    let json_str: String = row.get(0)?;
                    Ok(json_str)
                })?;

                for row in rows {
                    let json_str = row?;
                    match serde_json::from_str::<SystemData>(&json_str) {
                        Ok(data) => results.push(data),
                        Err(e) => warn!("无法解析系统数据JSON: {}", e),
                    }
                }
            } else {
                let mut stmt = conn.prepare(
                    "SELECT data_json FROM system_data
                     WHERE timestamp BETWEEN ?1 AND ?2
                     ORDER BY timestamp DESC"
                )?;
                let rows = stmt.query_map(params![
                    start_time.to_rfc3339(),
                    end_time.to_rfc3339()
                ], |row| {
                    let json_str: String = row.get(0)?;
                    Ok(json_str)
                })?;

                for row in rows {
                    let json_str = row?;
                    match serde_json::from_str::<SystemData>(&json_str) {
                        Ok(data) => results.push(data),
                        Err(e) => warn!("无法解析系统数据JSON: {}", e),
                    }
                }
            }

            Ok(results)
        }).await?
    }

    /// 查询预警数据
    pub async fn query_alerts(
        &self,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
        resolved: Option<bool>,
    ) -> Result<Vec<Alert>> {
        debug!("查询预警数据: {} 到 {}", start_time, end_time);

        let connection = Arc::clone(&self.connection);

        task::spawn_blocking(move || -> Result<Vec<Alert>> {
            let conn = connection.lock().unwrap();

            let sql = match resolved {
                Some(true) => "SELECT alert_id, alert_type, severity, message, timestamp, value, threshold
                              FROM alerts WHERE timestamp BETWEEN ?1 AND ?2 AND resolved = 1
                              ORDER BY timestamp DESC",
                Some(false) => "SELECT alert_id, alert_type, severity, message, timestamp, value, threshold
                               FROM alerts WHERE timestamp BETWEEN ?1 AND ?2 AND resolved = 0
                               ORDER BY timestamp DESC",
                None => "SELECT alert_id, alert_type, severity, message, timestamp, value, threshold
                         FROM alerts WHERE timestamp BETWEEN ?1 AND ?2
                         ORDER BY timestamp DESC",
            };

            let mut stmt = conn.prepare(sql)?;
            let rows = stmt.query_map(params![
                start_time.to_rfc3339(),
                end_time.to_rfc3339()
            ], |row| {
                let alert_type_str: String = row.get(1)?;
                let severity_str: String = row.get(2)?;
                let timestamp_str: String = row.get(4)?;

                Ok(Alert {
                    id: row.get(0)?,
                    alert_type: serde_json::from_str(&format!("\"{}\"", alert_type_str)).unwrap_or_default(),
                    severity: serde_json::from_str(&format!("\"{}\"", severity_str)).unwrap_or_default(),
                    message: row.get(3)?,
                    timestamp: DateTime::parse_from_rfc3339(&timestamp_str)
                        .map(|dt| dt.with_timezone(&Utc))
                        .unwrap_or_else(|_| Utc::now()),
                    value: row.get(5)?,
                    threshold: row.get(6)?,
                })
            })?;

            let mut results = Vec::new();
            for row in rows {
                match row {
                    Ok(alert) => results.push(alert),
                    Err(e) => warn!("无法解析预警数据: {}", e),
                }
            }

            Ok(results)
        }).await?
    }

    /// 清理过期数据
    pub async fn cleanup_old_data(&self) -> Result<()> {
        info!("开始清理过期数据");

        let retention_days = self.config.data_retention_days;
        let cutoff_time = Utc::now() - Duration::days(retention_days as i64);

        let connection = Arc::clone(&self.connection);

        task::spawn_blocking(move || -> Result<()> {
            let conn = connection.lock().unwrap();
            let tx = conn.unchecked_transaction()?;

            let cutoff_str = cutoff_time.to_rfc3339();

            // 清理各个表的过期数据
            let tables = vec![
                "system_data",
                "disk_data",
                "network_data",
                "temperature_data",
                "storage_health_data",
                "alerts"
            ];

            let mut total_deleted = 0;

            for table in tables {
                let deleted = tx.execute(
                    &format!("DELETE FROM {} WHERE timestamp < ?1", table),
                    params![cutoff_str],
                )?;

                if deleted > 0 {
                    info!("从表{}删除了{}条过期记录", table, deleted);
                    total_deleted += deleted;
                }
            }

            // 执行VACUUM以回收空间
            tx.commit()?;
            conn.execute("VACUUM", [])?;

            info!("数据清理完成，共删除{}条记录", total_deleted);
            Ok(())
        }).await??;

        Ok(())
    }

    /// 获取数据库统计信息
    pub async fn get_database_stats(&self) -> Result<DatabaseStats> {
        debug!("获取数据库统计信息");

        let connection = Arc::clone(&self.connection);

        task::spawn_blocking(move || -> Result<DatabaseStats> {
            let conn = connection.lock().unwrap();

            let mut stats = DatabaseStats::default();

            // 获取各表的记录数
            stats.system_data_count = conn.query_row(
                "SELECT COUNT(*) FROM system_data", [], |row| row.get(0)
            ).unwrap_or(0i64) as u64;

            stats.disk_data_count = conn.query_row(
                "SELECT COUNT(*) FROM disk_data", [], |row| row.get(0)
            ).unwrap_or(0i64) as u64;

            stats.network_data_count = conn.query_row(
                "SELECT COUNT(*) FROM network_data", [], |row| row.get(0)
            ).unwrap_or(0i64) as u64;

            stats.temperature_data_count = conn.query_row(
                "SELECT COUNT(*) FROM temperature_data", [], |row| row.get(0)
            ).unwrap_or(0i64) as u64;

            stats.storage_health_data_count = conn.query_row(
                "SELECT COUNT(*) FROM storage_health_data", [], |row| row.get(0)
            ).unwrap_or(0i64) as u64;

            stats.alerts_count = conn.query_row(
                "SELECT COUNT(*) FROM alerts", [], |row| row.get(0)
            ).unwrap_or(0i64) as u64;

            // 获取数据库文件大小
            let page_count: i64 = conn.query_row("PRAGMA page_count", [], |row| row.get(0)).unwrap_or(0);
            let page_size: i64 = conn.query_row("PRAGMA page_size", [], |row| row.get(0)).unwrap_or(0);
            stats.database_size_bytes = (page_count * page_size) as u64;

            // 获取最早和最新的记录时间
            if let Ok(earliest) = conn.query_row(
                "SELECT MIN(timestamp) FROM system_data",
                [],
                |row| row.get::<_, Option<String>>(0),
            ) {
                if let Some(earliest_str) = earliest {
                    stats.earliest_record = DateTime::parse_from_rfc3339(&earliest_str)
                        .map(|dt| dt.with_timezone(&Utc))
                        .ok();
                }
            }

            if let Ok(latest) = conn.query_row(
                "SELECT MAX(timestamp) FROM system_data",
                [],
                |row| row.get::<_, Option<String>>(0),
            ) {
                if let Some(latest_str) = latest {
                    stats.latest_record = DateTime::parse_from_rfc3339(&latest_str)
                        .map(|dt| dt.with_timezone(&Utc))
                        .ok();
                }
            }

            Ok(stats)
        }).await?
    }
}

/// 数据库统计信息
#[derive(Debug, Default)]
pub struct DatabaseStats {
    pub system_data_count: u64,
    pub disk_data_count: u64,
    pub network_data_count: u64,
    pub temperature_data_count: u64,
    pub storage_health_data_count: u64,
    pub alerts_count: u64,
    pub database_size_bytes: u64,
    pub earliest_record: Option<DateTime<Utc>>,
    pub latest_record: Option<DateTime<Utc>>,
}

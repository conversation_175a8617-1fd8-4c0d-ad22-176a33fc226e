use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
pub(crate) type inode_size_t = libc::fsfilcnt_t;
/// 系统数据结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemData {
    pub timestamp: DateTime<Utc>,
    pub cpu: CpuData,
    pub memory: MemoryData,
    pub disk: Vec<DiskData>,
    pub network: NetworkData,
    pub temperature: Vec<TemperatureData>,
    pub storage_health: StorageHealthData,
    pub filesystem: Vec<FilesystemData>,
}

/// CPU数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CpuData {
    pub usage_percent: f64,
    pub load_average: LoadAverage,
    pub per_core_usage: Vec<f64>,
    pub frequency: Option<u64>, // MHz
}

/// 负载平均值
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadAverage {
    pub one_minute: f64,
    pub five_minutes: f64,
    pub fifteen_minutes: f64,
}

/// 内存数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct MemoryData {
    pub total: u64,
    pub used: u64,
    pub available: u64,
    pub usage_percent: f64,
    pub swap_total: u64,
    pub swap_used: u64,
    pub swap_usage_percent: f64,
}

/// 磁盘数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DiskData {
    pub device: String,
    pub mount_point: String,
    pub total_space: u64,
    pub used_space: u64,
    pub available_space: u64,
    pub usage_percent: f64,
    pub inodes_total: Option<inode_size_t>,
    pub inodes_used: Option<inode_size_t>,
    pub inodes_usage_percent: Option<f64>,
}

/// 网络数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkData {
    pub interfaces: Vec<NetworkInterface>,
    pub connectivity: NetworkConnectivity,
    pub services: NetworkServices,
}

/// 网络接口
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkInterface {
    pub name: String,
    pub ip_address: Option<String>,
    pub is_up: bool,
    pub bytes_sent: u64,
    pub bytes_received: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
    pub errors_in: u64,
    pub errors_out: u64,
}

/// 网络连通性
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConnectivity {
    pub gateway_reachable: bool,
    pub dns_working: bool,
    pub internet_access: bool,
    pub gateway_ip: Option<String>,
    pub dns_servers: Vec<String>,
}

/// 网络服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkServices {
    pub ssh_status: ServiceStatus,
    pub ntp_status: ServiceStatus,
    pub dns_status: ServiceStatus,
}

/// 服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ServiceStatus {
    Running,
    Stopped,
    Failed,
    Unknown,
}

/// 温度数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TemperatureData {
    pub sensor_name: String,
    pub temperature: Option<f64>, // 摄氏度
    pub critical_temp: Option<f64>,
    pub max_temp: Option<f64>,
}

/// 存储健康数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageHealthData {
    pub devices: Vec<StorageDevice>,
}

/// 存储设备
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageDevice {
    pub device: String,
    pub device_type: StorageType,
    pub health_percentage: f64,
    pub detail: Option<DetailedHealthData>,
}
/// detailed health statistics
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DetailedHealthData {
    pub max_erase: u64,
    pub min_erase: u64,
    pub avg_erase: u64,
    pub totol_wr: u64,
    pub totol_rd: u64,
}

/// 存储类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum StorageType {
    SSD,
    HDD,
    #[allow(non_camel_case_types)]
    eMMC,
    SDCard,
    Unknown,
}

/// 文件系统数据
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
#[expect(dead_code)]
pub struct FilesystemData {
    pub path: String,
    pub size_bytes: u64,
    pub is_directory: bool,
    pub last_modified: DateTime<Utc>,
}

/// 预警数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Alert {
    pub id: String,
    pub alert_type: AlertType,
    pub severity: AlertSeverity,
    pub message: String,
    pub timestamp: DateTime<Utc>,
    pub value: Option<f64>,
    pub threshold: Option<f64>,
}

/// 预警类型
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum AlertType {
    #[default]
    CpuUsage,
    MemoryUsage,
    DiskUsage,
    DiskInodeUsage,
    Temperature,
    StorageHealth,
    NetworkConnectivity,
    ServiceDown,
    PowerLoss,
    FileSystemChange,
}

/// 预警严重程度
#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub enum AlertSeverity {
    #[default]
    Info,
    Warning,
    Critical,
    Emergency,
}

/// 配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitorConfig {
    pub watch_interval: u64, // 秒
    pub data_retention_days: u32,
    pub database_path: String,
    pub log_path: String,
    pub thresholds: AlertThresholds,
    pub monitoring: MonitoringConfig,
}

/// 预警阈值配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AlertThresholds {
    pub cpu_usage_warning: f64,
    pub cpu_usage_critical: f64,
    pub memory_usage_warning: f64,
    pub memory_usage_critical: f64,
    pub disk_usage_warning: f64,
    pub disk_usage_critical: f64,
    pub inode_usage_warning: f64,
    pub inode_usage_critical: f64,
    pub temperature_warning: f64,
    pub temperature_critical: f64,
    pub storage_health_warning: f64,
    pub storage_health_critical: f64,
}

/// 监控配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonitoringConfig {
    pub enable_cpu: bool,
    pub enable_memory: bool,
    pub enable_disk: bool,
    pub enable_network: bool,
    pub enable_temperature: bool,
    pub enable_storage_health: bool,
    pub enable_filesystem: bool,
    pub filesystem_paths: Vec<String>,
    pub network_check_hosts: Vec<String>,
}

impl Default for MonitorConfig {
    fn default() -> Self {
        Self {
            watch_interval: 60,
            data_retention_days: 30,
            database_path: "/var/lib/mon/data.db".to_string(),
            log_path: "/var/log/mon/".to_string(),
            thresholds: AlertThresholds::default(),
            monitoring: MonitoringConfig::default(),
        }
    }
}

impl Default for AlertThresholds {
    fn default() -> Self {
        Self {
            cpu_usage_warning: 80.0,
            cpu_usage_critical: 95.0,
            memory_usage_warning: 80.0,
            memory_usage_critical: 95.0,
            disk_usage_warning: 80.0,
            disk_usage_critical: 95.0,
            inode_usage_warning: 80.0,
            inode_usage_critical: 95.0,
            temperature_warning: 70.0,
            temperature_critical: 85.0,
            storage_health_warning: 20.0,
            storage_health_critical: 10.0,
        }
    }
}

impl Default for MonitoringConfig {
    fn default() -> Self {
        Self {
            enable_cpu: true,
            enable_memory: true,
            enable_disk: true,
            enable_network: true,
            enable_temperature: true,
            enable_storage_health: true,
            enable_filesystem: false,
            filesystem_paths: vec!["/etc".to_string(), "/var/log".to_string()],
            network_check_hosts: vec!["*******".to_string(), "*******".to_string()],
        }
    }
}

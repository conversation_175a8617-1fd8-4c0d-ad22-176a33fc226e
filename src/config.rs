use anyhow::{Result, Context};
use log::{info, warn, debug};
use notify::{Watcher, RecursiveMode};
use notify_debouncer_mini::{new_debouncer, DebounceEventResult};
use std::fs;
use std::path::{Path, PathBuf};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::sync::mpsc;

use crate::types::MonitorConfig;

/// 配置管理器
pub struct Config {
    config: Arc<Mutex<MonitorConfig>>,
    config_path: PathBuf,
    _watcher: Option<notify_debouncer_mini::Debouncer<notify::RecommendedWatcher>>,
    update_sender: Option<mpsc::UnboundedSender<MonitorConfig>>,
}

impl Config {
    /// 加载配置
    pub async fn load() -> Result<Self> {
        let config_path = Self::get_config_path()?;
        
        // 如果配置文件不存在，创建默认配置
        if !config_path.exists() {
            info!("配置文件不存在，创建默认配置: {}", config_path.display());
            Self::create_default_config(&config_path)?;
        }
        
        // 读取配置文件
        let config = Self::read_config_file(&config_path)?;
        info!("配置加载完成: {}", config_path.display());
        
        Ok(Self {
            config: Arc::new(Mutex::new(config)),
            config_path,
            _watcher: None,
            update_sender: None,
        })
    }
    
    /// 获取配置路径
    fn get_config_path() -> Result<PathBuf> {
        // 优先使用环境变量指定的配置文件路径
        if let Ok(config_path) = std::env::var("MON_CONFIG_PATH") {
            return Ok(PathBuf::from(config_path));
        }
        
        // 检查常见的配置文件位置
        let possible_paths = vec![
            "/etc/mon/config.toml",
            "/usr/local/etc/mon/config.toml",
            "./config.toml",
            "~/.config/mon/config.toml",
        ];
        
        for path_str in possible_paths {
            let path = if path_str.starts_with('~') {
                if let Ok(home) = std::env::var("HOME") {
                    PathBuf::from(path_str.replace('~', &home))
                } else {
                    continue;
                }
            } else {
                PathBuf::from(path_str)
            };
            
            if path.exists() {
                return Ok(path);
            }
        }
        
        // 默认配置文件路径
        Ok(PathBuf::from("/etc/mon/config.toml"))
    }
    
    /// 创建默认配置文件
    fn create_default_config(config_path: &Path) -> Result<()> {
        // 确保配置目录存在
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent)
                .context("无法创建配置目录")?;
        }
        
        let default_config = MonitorConfig::default();
        let config_content = toml::to_string_pretty(&default_config)
            .context("无法序列化默认配置")?;
        
        fs::write(config_path, config_content)
            .context("无法写入默认配置文件")?;
        
        info!("默认配置文件已创建: {}", config_path.display());
        Ok(())
    }
    
    /// 读取配置文件
    fn read_config_file(config_path: &Path) -> Result<MonitorConfig> {
        let content = fs::read_to_string(config_path)
            .context("无法读取配置文件")?;
        
        let config: MonitorConfig = toml::from_str(&content)
            .context("无法解析配置文件")?;
        
        // 验证配置
        Self::validate_config(&config)?;
        
        Ok(config)
    }
    
    /// 验证配置
    fn validate_config(config: &MonitorConfig) -> Result<()> {
        if config.watch_interval == 0 {
            return Err(anyhow::anyhow!("监控间隔不能为0"));
        }
        
        if config.data_retention_days == 0 {
            return Err(anyhow::anyhow!("数据保留天数不能为0"));
        }
        
        // 验证数据库路径的父目录是否存在
        let db_path = Path::new(&config.database_path);
        if let Some(parent) = db_path.parent() {
            if !parent.exists() {
                warn!("数据库目录不存在，将尝试创建: {}", parent.display());
                fs::create_dir_all(parent)
                    .context("无法创建数据库目录")?;
            }
        }
        
        // 验证日志路径
        let log_path = Path::new(&config.log_path);
        if !log_path.exists() {
            warn!("日志目录不存在，将尝试创建: {}", log_path.display());
            fs::create_dir_all(log_path)
                .context("无法创建日志目录")?;
        }
        
        Ok(())
    }
    
    /// 获取当前配置
    pub fn get(&self) -> MonitorConfig {
        self.config.lock().unwrap().clone()
    }
    
    /// 更新配置
    pub fn update(&self, new_config: MonitorConfig) -> Result<()> {
        // 验证新配置
        Self::validate_config(&new_config)?;
        
        // 更新内存中的配置
        {
            let mut config = self.config.lock().unwrap();
            *config = new_config.clone();
        }
        
        // 保存到文件
        self.save_to_file(&new_config)?;
        
        // 通知配置更新
        if let Some(sender) = &self.update_sender {
            let _ = sender.send(new_config);
        }
        
        info!("配置已更新");
        Ok(())
    }
    
    /// 保存配置到文件
    fn save_to_file(&self, config: &MonitorConfig) -> Result<()> {
        let config_content = toml::to_string_pretty(config)
            .context("无法序列化配置")?;
        
        fs::write(&self.config_path, config_content)
            .context("无法写入配置文件")?;
        
        debug!("配置已保存到文件: {}", self.config_path.display());
        Ok(())
    }
    
    /// 启动配置文件监控
    pub fn start_watching(&mut self) -> Result<mpsc::UnboundedReceiver<MonitorConfig>> {
        let (tx, rx) = mpsc::unbounded_channel();
        self.update_sender = Some(tx.clone());
        
        let config_path = self.config_path.clone();
        let config_arc = Arc::clone(&self.config);
        
        let mut debouncer = new_debouncer(
            Duration::from_secs(2),
            move |result: DebounceEventResult| {
                match result {
                    Ok(events) => {
                        for event in events {
                            if event.path == config_path {
                                debug!("检测到配置文件变化: {}", config_path.display());
                                
                                match Self::read_config_file(&config_path) {
                                    Ok(new_config) => {
                                        // 更新内存中的配置
                                        {
                                            let mut config = config_arc.lock().unwrap();
                                            *config = new_config.clone();
                                        }
                                        
                                        // 发送更新通知
                                        if let Err(e) = tx.send(new_config) {
                                            warn!("发送配置更新通知失败: {}", e);
                                        } else {
                                            info!("配置文件已重新加载");
                                        }
                                    },
                                    Err(e) => {
                                        warn!("重新加载配置文件失败: {}", e);
                                    }
                                }
                            }
                        }
                    },
                    Err(e) => {
                        warn!("配置文件监控错误: {}", e);
                    }
                }
            }
        ).context("无法创建文件监控器")?;
        
        // 监控配置文件
        debouncer.watcher().watch(&self.config_path, RecursiveMode::NonRecursive)
            .context("无法监控配置文件")?;
        
        self._watcher = Some(debouncer);
        
        info!("配置文件监控已启动: {}", self.config_path.display());
        Ok(rx)
    }
    
    /// 重新加载配置
    pub fn reload(&self) -> Result<()> {
        let new_config = Self::read_config_file(&self.config_path)?;
        
        {
            let mut config = self.config.lock().unwrap();
            *config = new_config.clone();
        }
        
        if let Some(sender) = &self.update_sender {
            let _ = sender.send(new_config);
        }
        
        info!("配置已重新加载");
        Ok(())
    }
    
    /// 获取配置文件路径
    pub fn get_config_file_path(&self) -> &Path {
        &self.config_path
    }
    
    /// 导出配置为JSON格式
    pub fn export_json(&self) -> Result<String> {
        let config = self.get();
        serde_json::to_string_pretty(&config)
            .context("无法序列化配置为JSON")
    }
    
    /// 从JSON导入配置
    pub fn import_json(&self, json_content: &str) -> Result<()> {
        let new_config: MonitorConfig = serde_json::from_str(json_content)
            .context("无法解析JSON配置")?;
        
        self.update(new_config)
    }
}

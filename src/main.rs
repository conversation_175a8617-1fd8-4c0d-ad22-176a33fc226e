mod types;
mod config;
mod storage;
mod alert;
mod logger;

// 暂时简化收集器模块
// mod simple_collector;
mod collector;

use anyhow::Result;
use log::{info, error, warn};
use std::time::Duration;
use tokio::time::interval;
use tokio::signal;
use tokio::select;

use config::Config;
use collector::SystemCollector;
use storage::DataStorage;
use alert::AlertSystem;
use logger::{SystemLogger, LogLevel};

#[tokio::main]
async fn main() -> Result<()> {
    // 初始化日志系统
    env_logger::init();

    info!("启动系统监控程序");

    // 加载配置
    let mut config = Config::load().await?;
    info!("配置加载完成");

    // 启动配置文件监控
    let mut config_updates = config.start_watching()?;

    // 初始化各个模块
    let current_config = config.get();
    let mut collector = SystemCollector::new(&current_config)?;
    let mut storage = DataStorage::new(&current_config).await?;
    let mut alert_system = AlertSystem::new(&current_config)?;
    let mut system_logger = SystemLogger::new(&current_config)?;

    info!("所有模块初始化完成");

    // 创建定时任务
    let mut monitoring_interval = interval(Duration::from_secs(current_config.watch_interval));
    let mut cleanup_interval = interval(Duration::from_secs(24 * 60 * 60)); // 每天清理一次
    let mut log_rotation_interval = interval(Duration::from_secs(7 * 24 * 60 * 60)); // 每周轮转一次

    // 主循环
    loop {
        select! {
            // 监控数据收集
            _ = monitoring_interval.tick() => {
                if let Err(e) = run_monitoring_cycle(&mut collector, &mut storage, &mut alert_system, &mut system_logger).await {
                    error!("监控周期执行失败: {}", e);
                    let _ = system_logger.log_error(&format!("监控周期执行失败: {}", e)).await;
                }
            },

            // 配置更新
            Some(new_config) = config_updates.recv() => {
                info!("收到配置更新通知");
                if let Err(e) = handle_config_update(&mut collector, &mut alert_system, &new_config, &mut monitoring_interval).await {
                    error!("处理配置更新失败: {}", e);
                    let _ = system_logger.log_error(&format!("处理配置更新失败: {}", e)).await;
                }
            },

            // 数据清理
            _ = cleanup_interval.tick() => {
                info!("开始执行数据清理任务");
                if let Err(e) = storage.cleanup_old_data().await {
                    error!("数据清理失败: {}", e);
                    let _ = system_logger.log_error(&format!("数据清理失败: {}", e)).await;
                }
            },

            // 日志轮转
            _ = log_rotation_interval.tick() => {
                info!("开始执行日志轮转任务");
                if let Err(e) = system_logger.rotate_logs().await {
                    error!("日志轮转失败: {}", e);
                }

                // 清理30天前的日志
                if let Err(e) = system_logger.cleanup_old_logs(30).await {
                    error!("清理旧日志失败: {}", e);
                }
            },

            // 处理系统信号
            _ = signal::ctrl_c() => {
                info!("收到退出信号，正在关闭程序");
                let _ = system_logger.log_system(LogLevel::Info, "系统监控程序正常退出").await;
                break;
            }
        }
    }

    info!("系统监控程序已退出");
    Ok(())
}

/// 处理配置更新
async fn handle_config_update(
    collector: &mut SystemCollector,
    alert_system: &mut AlertSystem,
    new_config: &types::MonitorConfig,
    monitoring_interval: &mut tokio::time::Interval,
) -> Result<()> {
    info!("应用新配置");

    // 更新收集器配置
    collector.update_config(new_config.clone())?;

    // 更新预警系统配置
    alert_system.update_config(new_config.clone());

    // 如果监控间隔发生变化，重新创建定时器
    let new_interval = Duration::from_secs(new_config.watch_interval);
    *monitoring_interval = interval(new_interval);

    info!("配置更新完成");
    Ok(())
}

/// 执行监控周期
async fn run_monitoring_cycle(
    collector: &mut SystemCollector,
    storage: &mut DataStorage,
    alert_system: &mut AlertSystem,
    system_logger: &mut SystemLogger,
) -> Result<()> {
    let start_time = std::time::Instant::now();

    // 收集系统信息
    let system_data = match collector.collect_all().await {
        Ok(data) => data,
        Err(e) => {
            error!("收集系统信息失败: {}", e);
            system_logger.log_error(&format!("收集系统信息失败: {}", e)).await?;
            return Err(e);
        }
    };

    // 存储数据
    if let Err(e) = storage.store_system_data(&system_data).await {
        error!("存储系统数据失败: {}", e);
        system_logger.log_error(&format!("存储系统数据失败: {}", e)).await?;
        return Err(e);
    }

    // 检查预警条件
    match alert_system.check_alerts(&system_data).await {
        Ok(Some(alerts)) => {
            info!("触发了{}个预警", alerts.len());
            for alert in alerts {
                // 记录预警
                system_logger.log_alert(&alert).await?;

                // 存储预警到数据库
                if let Err(e) = storage.store_alert(&alert).await {
                    warn!("存储预警失败: {}", e);
                }

                // 这里可以添加其他通知方式，如发送DBus信号、邮件通知等
                send_alert_notification(&alert).await?;
            }
        },
        Ok(None) => {
            // 没有新预警
        },
        Err(e) => {
            error!("检查预警条件失败: {}", e);
            system_logger.log_error(&format!("检查预警条件失败: {}", e)).await?;
        }
    }

    let duration = start_time.elapsed();
    if duration > Duration::from_secs(10) {
        warn!("监控周期执行时间过长: {:?}", duration);
    }

    Ok(())
}

/// 发送预警通知
async fn send_alert_notification(alert: &types::Alert) -> Result<()> {
    // 这里可以实现各种通知方式
    // 例如：DBus信号、邮件、短信、Webhook等

    // 示例：发送DBus信号（需要添加相应的依赖）
    info!("发送预警通知: {} - {}", alert.id, alert.message);

    // TODO: 实现具体的通知逻辑
    // 例如：
    // - 发送DBus信号给其他系统组件
    // - 发送HTTP请求到监控中心
    // - 写入特定的通知文件
    // - 触发LED指示灯等硬件通知

    Ok(())
}

/// 检测系统断电
async fn detect_power_loss() -> Result<bool> {
    // 这里可以实现断电检测逻辑
    // 例如：
    // - 监控UPS状态
    // - 检查电源管理芯片状态
    // - 监控电压传感器
    // - 检查系统日志中的断电记录

    // 示例实现：检查/proc/acpi/battery状态（如果存在）
    if let Ok(content) = tokio::fs::read_to_string("/proc/acpi/battery/BAT0/state").await {
        if content.contains("discharging") {
            return Ok(true);
        }
    }

    Ok(false)
}

/// 系统启动时的初始化检查
async fn perform_startup_checks(system_logger: &SystemLogger) -> Result<()> {
    info!("执行启动检查");

    // 检查是否从断电中恢复
    if let Ok(true) = detect_power_loss().await {
        system_logger.log_power_restore().await?;
    }

    // 检查关键目录是否存在
    let critical_dirs = vec!["/etc", "/var", "/tmp"];
    for dir in critical_dirs {
        if !tokio::fs::metadata(dir).await.is_ok() {
            let msg = format!("关键目录缺失: {}", dir);
            error!("{}", msg);
            system_logger.log_error(&msg).await?;
        }
    }

    // 检查磁盘空间
    // TODO: 添加磁盘空间检查逻辑

    info!("启动检查完成");
    Ok(())
}

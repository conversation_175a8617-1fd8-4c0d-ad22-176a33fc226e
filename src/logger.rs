use anyhow::{Result, Context};
use chrono::{DateTime, Utc};
use log::{info, warn, debug, error};
use serde::{Deserialize, Serialize};
use std::fs::{File, OpenOptions};
use std::io::{Write, BufWriter};
use std::path::Path;
use std::sync::{Arc, Mutex};
use tokio::fs;

use crate::types::{MonitorConfig, Alert};

/// 日志事件类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogEventType {
    SystemStart,
    SystemStop,
    ConfigUpdate,
    AlertTriggered,
    AlertResolved,
    DataCollectionError,
    StorageError,
    NetworkError,
    PowerLoss,
    PowerRestore,
    ServiceStart,
    ServiceStop,
    FileSystemChange,
    Custom(String),
}

/// 日志事件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LogEvent {
    pub timestamp: DateTime<Utc>,
    pub event_type: LogEventType,
    pub level: LogLevel,
    pub message: String,
    pub details: Option<serde_json::Value>,
    pub source: String,
}

/// 日志级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
    Critical,
}

/// 系统日志记录器
pub struct SystemLogger {
    config: MonitorConfig,
    log_file: Arc<Mutex<BufWriter<File>>>,
    event_log_file: Arc<Mutex<BufWriter<File>>>,
    alert_log_file: Arc<Mutex<BufWriter<File>>>,
}

impl SystemLogger {
    /// 创建新的系统日志记录器
    pub fn new(config: &MonitorConfig) -> Result<Self> {
        info!("初始化系统日志记录器");
        
        // 确保日志目录存在
        let log_path = Path::new(&config.log_path);
        std::fs::create_dir_all(log_path)
            .context("无法创建日志目录")?;
        
        // 创建日志文件
        let log_file_path = log_path.join("system.log");
        let event_log_file_path = log_path.join("events.log");
        let alert_log_file_path = log_path.join("alerts.log");
        
        let log_file = Self::create_log_file(&log_file_path)?;
        let event_log_file = Self::create_log_file(&event_log_file_path)?;
        let alert_log_file = Self::create_log_file(&alert_log_file_path)?;
        
        let logger = Self {
            config: config.clone(),
            log_file: Arc::new(Mutex::new(BufWriter::new(log_file))),
            event_log_file: Arc::new(Mutex::new(BufWriter::new(event_log_file))),
            alert_log_file: Arc::new(Mutex::new(BufWriter::new(alert_log_file))),
        };
        
        // 记录系统启动事件
        let start_event = LogEvent {
            timestamp: Utc::now(),
            event_type: LogEventType::SystemStart,
            level: LogLevel::Info,
            message: "系统监控程序启动".to_string(),
            details: None,
            source: "system".to_string(),
        };
        
        if let Err(e) = logger.log_event_internal(&start_event) {
            warn!("记录启动事件失败: {}", e);
        }
        
        info!("系统日志记录器初始化完成");
        Ok(logger)
    }
    
    /// 创建日志文件
    fn create_log_file(path: &Path) -> Result<File> {
        OpenOptions::new()
            .create(true)
            .append(true)
            .open(path)
            .context("无法创建日志文件")
    }
    
    /// 记录系统日志
    pub async fn log_system(&self, level: LogLevel, message: &str) -> Result<()> {
        let log_entry = format!(
            "[{}] [{}] {}\n",
            Utc::now().format("%Y-%m-%d %H:%M:%S%.3f UTC"),
            self.format_log_level(&level),
            message
        );
        
        let log_file = Arc::clone(&self.log_file);
        tokio::task::spawn_blocking(move || -> Result<()> {
            let mut writer = log_file.lock().unwrap();
            writer.write_all(log_entry.as_bytes())
                .context("无法写入系统日志")?;
            writer.flush()
                .context("无法刷新系统日志")?;
            Ok(())
        }).await??;
        
        Ok(())
    }
    
    /// 记录事件
    pub async fn log_event(&self, event: LogEvent) -> Result<()> {
        self.log_event_internal(&event)?;
        
        // 同时记录到系统日志
        self.log_system(
            event.level.clone(),
            &format!("[{}] {}", self.format_event_type(&event.event_type), event.message)
        ).await?;
        
        Ok(())
    }
    
    /// 内部事件记录方法
    fn log_event_internal(&self, event: &LogEvent) -> Result<()> {
        let event_json = serde_json::to_string(event)
            .context("无法序列化事件")?;
        
        let log_entry = format!("{}\n", event_json);
        
        let event_log_file = Arc::clone(&self.event_log_file);
        let mut writer = event_log_file.lock().unwrap();
        writer.write_all(log_entry.as_bytes())
            .context("无法写入事件日志")?;
        writer.flush()
            .context("无法刷新事件日志")?;
        
        Ok(())
    }
    
    /// 记录预警
    pub async fn log_alert(&self, alert: &Alert) -> Result<()> {
        debug!("记录预警: {}", alert.id);
        
        // 创建预警事件
        let alert_event = LogEvent {
            timestamp: alert.timestamp,
            event_type: LogEventType::AlertTriggered,
            level: self.alert_severity_to_log_level(&alert.severity),
            message: alert.message.clone(),
            details: Some(serde_json::to_value(alert)?),
            source: "alert_system".to_string(),
        };
        
        // 记录到事件日志
        self.log_event(alert_event).await?;
        
        // 记录到专门的预警日志
        let alert_json = serde_json::to_string(alert)
            .context("无法序列化预警")?;
        
        let log_entry = format!("{}\n", alert_json);
        
        let alert_log_file = Arc::clone(&self.alert_log_file);
        tokio::task::spawn_blocking(move || -> Result<()> {
            let mut writer = alert_log_file.lock().unwrap();
            writer.write_all(log_entry.as_bytes())
                .context("无法写入预警日志")?;
            writer.flush()
                .context("无法刷新预警日志")?;
            Ok(())
        }).await??;
        
        Ok(())
    }
    
    /// 记录错误
    pub async fn log_error(&self, error_message: &str) -> Result<()> {
        let error_event = LogEvent {
            timestamp: Utc::now(),
            event_type: LogEventType::Custom("error".to_string()),
            level: LogLevel::Error,
            message: error_message.to_string(),
            details: None,
            source: "system".to_string(),
        };
        
        self.log_event(error_event).await?;
        error!("{}", error_message);
        
        Ok(())
    }
    
    /// 记录配置更新
    pub async fn log_config_update(&self, old_config: &MonitorConfig, new_config: &MonitorConfig) -> Result<()> {
        let config_event = LogEvent {
            timestamp: Utc::now(),
            event_type: LogEventType::ConfigUpdate,
            level: LogLevel::Info,
            message: "配置已更新".to_string(),
            details: Some(serde_json::json!({
                "old_config": old_config,
                "new_config": new_config
            })),
            source: "config_manager".to_string(),
        };
        
        self.log_event(config_event).await?;
        
        Ok(())
    }
    
    /// 记录断电事件
    pub async fn log_power_loss(&self) -> Result<()> {
        let power_event = LogEvent {
            timestamp: Utc::now(),
            event_type: LogEventType::PowerLoss,
            level: LogLevel::Critical,
            message: "检测到系统断电".to_string(),
            details: None,
            source: "power_monitor".to_string(),
        };
        
        self.log_event(power_event).await?;
        
        Ok(())
    }
    
    /// 记录恢复供电事件
    pub async fn log_power_restore(&self) -> Result<()> {
        let power_event = LogEvent {
            timestamp: Utc::now(),
            event_type: LogEventType::PowerRestore,
            level: LogLevel::Info,
            message: "系统供电已恢复".to_string(),
            details: None,
            source: "power_monitor".to_string(),
        };
        
        self.log_event(power_event).await?;
        
        Ok(())
    }
    
    /// 记录文件系统变化
    pub async fn log_filesystem_change(&self, path: &str, change_type: &str) -> Result<()> {
        let fs_event = LogEvent {
            timestamp: Utc::now(),
            event_type: LogEventType::FileSystemChange,
            level: LogLevel::Info,
            message: format!("文件系统变化: {} - {}", path, change_type),
            details: Some(serde_json::json!({
                "path": path,
                "change_type": change_type
            })),
            source: "filesystem_monitor".to_string(),
        };
        
        self.log_event(fs_event).await?;
        
        Ok(())
    }
    
    /// 轮转日志文件
    pub async fn rotate_logs(&self) -> Result<()> {
        info!("开始轮转日志文件");
        
        let log_path = Path::new(&self.config.log_path);
        
        // 轮转各个日志文件
        self.rotate_log_file(&log_path.join("system.log")).await?;
        self.rotate_log_file(&log_path.join("events.log")).await?;
        self.rotate_log_file(&log_path.join("alerts.log")).await?;
        
        info!("日志文件轮转完成");
        Ok(())
    }
    
    /// 轮转单个日志文件
    async fn rotate_log_file(&self, log_file_path: &Path) -> Result<()> {
        if !log_file_path.exists() {
            return Ok(());
        }
        
        let metadata = fs::metadata(log_file_path).await?;
        let file_size = metadata.len();
        
        // 如果文件大小超过10MB，进行轮转
        if file_size > 10 * 1024 * 1024 {
            let timestamp = Utc::now().format("%Y%m%d_%H%M%S");
            let rotated_name = format!(
                "{}.{}",
                log_file_path.to_string_lossy(),
                timestamp
            );
            
            fs::rename(log_file_path, &rotated_name).await
                .context("无法轮转日志文件")?;
            
            info!("日志文件已轮转: {} -> {}", log_file_path.display(), rotated_name);
        }
        
        Ok(())
    }
    
    /// 清理旧日志文件
    pub async fn cleanup_old_logs(&self, days: u32) -> Result<()> {
        info!("开始清理{}天前的日志文件", days);
        
        let log_path = Path::new(&self.config.log_path);
        let cutoff_time = Utc::now() - chrono::Duration::days(days as i64);
        
        let mut entries = fs::read_dir(log_path).await?;
        let mut deleted_count = 0;
        
        while let Some(entry) = entries.next_entry().await? {
            let path = entry.path();
            
            if let Ok(metadata) = entry.metadata().await {
                if let Ok(modified) = metadata.modified() {
                    let modified_utc: DateTime<Utc> = modified.into();
                    
                    if modified_utc < cutoff_time {
                        if let Err(e) = fs::remove_file(&path).await {
                            warn!("删除旧日志文件失败: {} - {}", path.display(), e);
                        } else {
                            debug!("删除旧日志文件: {}", path.display());
                            deleted_count += 1;
                        }
                    }
                }
            }
        }
        
        info!("清理完成，删除了{}个旧日志文件", deleted_count);
        Ok(())
    }
    
    /// 格式化日志级别
    fn format_log_level(&self, level: &LogLevel) -> &'static str {
        match level {
            LogLevel::Debug => "DEBUG",
            LogLevel::Info => "INFO",
            LogLevel::Warning => "WARN",
            LogLevel::Error => "ERROR",
            LogLevel::Critical => "CRIT",
        }
    }
    
    /// 格式化事件类型
    fn format_event_type<'a>(&self, event_type: &'a LogEventType) -> &'a str {
        match event_type {
            LogEventType::SystemStart => "SYSTEM_START",
            LogEventType::SystemStop => "SYSTEM_STOP",
            LogEventType::ConfigUpdate => "CONFIG_UPDATE",
            LogEventType::AlertTriggered => "ALERT_TRIGGERED",
            LogEventType::AlertResolved => "ALERT_RESOLVED",
            LogEventType::DataCollectionError => "DATA_COLLECTION_ERROR",
            LogEventType::StorageError => "STORAGE_ERROR",
            LogEventType::NetworkError => "NETWORK_ERROR",
            LogEventType::PowerLoss => "POWER_LOSS",
            LogEventType::PowerRestore => "POWER_RESTORE",
            LogEventType::ServiceStart => "SERVICE_START",
            LogEventType::ServiceStop => "SERVICE_STOP",
            LogEventType::FileSystemChange => "FILESYSTEM_CHANGE",
            LogEventType::Custom(name) => name,
        }
    }
    
    /// 将预警严重程度转换为日志级别
    fn alert_severity_to_log_level(&self, severity: &crate::types::AlertSeverity) -> LogLevel {
        match severity {
            crate::types::AlertSeverity::Info => LogLevel::Info,
            crate::types::AlertSeverity::Warning => LogLevel::Warning,
            crate::types::AlertSeverity::Critical => LogLevel::Error,
            crate::types::AlertSeverity::Emergency => LogLevel::Critical,
        }
    }
}

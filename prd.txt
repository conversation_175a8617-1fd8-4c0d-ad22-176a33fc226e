4.3 运行监视及预警
4.3.1 需求分析
该功能主要监控 CPU、内存、存储等系统资源的使用情况。目前，这部分功能已在智能运维中使用，考虑到其基础性和通用性，建议将其整合到系统平台中，以便其他程序能够复用，提升系统的整体效率和可维护性。
目前使用系统遇到的问题：
1.存储空间占满
2.存储损坏，表现为无法进入系统。
3.应用、系统卡死，如mcp2515驱动导致系统rtnetlink卡死。
4.排查系统是否发生断电依赖推理
5.关键文件缺失难以排查
具体功能需求：
1.支持设置全局监视间隔（WatchItv）。
2.监视系统级运行信息：
cpu负载、内存、磁盘空间占用、inode等系统监视。
存储寿命：每写完N扇区额外记录一次(N为总扇区数)。
网络状态：网络连通状态的改变，dns、ntp、ssh状态的改变，记录ip、网关、dns、路由的改变
文件系统：文件、文件夹大小监视
3.使用时序数据库记录系统级运行信息，方便后续统计和可视化
4.运行状态记录
5.预警：达到触发条件后向订阅信号方发送信号。
6.断电、启动的记录与上报

4.3.2 概要设计
******* 总体设计描述
核心部分为系统级和进程级的运行信息（cpu负载、内存占用等）。运行状态记录会定期将这些信息记录在本地时序数据库中。logger负责记录程序运行过程中的必要信息和异常，以及外部信号的触发，如“应用管理”完成了一次应用启动。
外部依赖关系：
系统平台其他工具的DBus信号输入。
linux内核cgroups
模块分解描述：
1.核心部分：
目的：
系统级和进程级信息监视和记录
功能列表：
1）系统级信息的查询：cpu负载、温度、内存占用、存储占用、存储读写速率、存储寿命、网络连接状态、dns、ntp、ssh服务状态
2）预警：cpu负载、内存占用、存储占用的阈值设置和预警、掉电预警
3）设置监视和上报间隔
4）查询进程状态
2.运行状态记录：
按监视间隔记录mgc.platform.Monit1的属性、信号触发、使用CreateProcMonit和CreateFileMonit创建的进程和文件监视信息、“应用管理”的信号到本地数据库
3.logger：
记录程序执行重要节点、异常、和监视信号的触发。
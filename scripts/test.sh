#!/bin/bash

# 系统监控程序测试脚本

set -e

echo "开始测试系统监控程序..."

# 配置变量
TEST_DIR="/tmp/mon_test"
CONFIG_FILE="./config.toml"
PROGRAM="./target/release/mon"

# 检查程序是否存在
if [ ! -f "$PROGRAM" ]; then
    echo "错误: 程序文件不存在，请先运行 'cargo build --release'"
    exit 1
fi

# 创建测试目录
echo "创建测试目录..."
mkdir -p "$TEST_DIR/data" "$TEST_DIR/logs"

# 清理之前的测试数据
echo "清理之前的测试数据..."
rm -f "$TEST_DIR/data/"* "$TEST_DIR/logs/"*

# 运行程序5秒
echo "运行程序5秒..."
RUST_LOG=info MON_CONFIG_PATH="$CONFIG_FILE" timeout 5s "$PROGRAM" || true

# 检查生成的文件
echo "检查生成的文件..."

if [ -f "$TEST_DIR/data/data.db" ]; then
    echo "✓ 数据库文件已创建"
    echo "  数据库大小: $(du -h "$TEST_DIR/data/data.db" | cut -f1)"
else
    echo "✗ 数据库文件未创建"
    exit 1
fi

if [ -f "$TEST_DIR/logs/events.log" ]; then
    echo "✓ 事件日志文件已创建"
    echo "  事件日志条数: $(wc -l < "$TEST_DIR/logs/events.log")"
    echo "  最新事件:"
    tail -1 "$TEST_DIR/logs/events.log" | jq -r '.message' 2>/dev/null || tail -1 "$TEST_DIR/logs/events.log"
else
    echo "✗ 事件日志文件未创建"
    exit 1
fi

# 检查数据库内容
echo "检查数据库内容..."
if command -v sqlite3 >/dev/null 2>&1; then
    echo "  数据库表:"
    sqlite3 "$TEST_DIR/data/data.db" ".tables"
    
    echo "  系统数据记录数:"
    sqlite3 "$TEST_DIR/data/data.db" "SELECT COUNT(*) FROM system_data;"
    
    echo "  最新系统数据时间戳:"
    sqlite3 "$TEST_DIR/data/data.db" "SELECT timestamp FROM system_data ORDER BY timestamp DESC LIMIT 1;" 2>/dev/null || echo "  无数据"
else
    echo "  sqlite3 未安装，跳过数据库内容检查"
fi

# 测试配置文件监控
echo "测试配置文件监控..."
cp "$CONFIG_FILE" "$CONFIG_FILE.backup"

# 修改配置文件
sed -i 's/watch_interval = 60/watch_interval = 30/' "$CONFIG_FILE"

echo "运行程序3秒以测试配置更新..."
RUST_LOG=info MON_CONFIG_PATH="$CONFIG_FILE" timeout 3s "$PROGRAM" || true

# 恢复配置文件
mv "$CONFIG_FILE.backup" "$CONFIG_FILE"

# 性能测试
echo "性能测试..."
echo "  程序大小: $(du -h "$PROGRAM" | cut -f1)"

# 内存使用测试
echo "内存使用测试..."
RUST_LOG=error MON_CONFIG_PATH="$CONFIG_FILE" timeout 2s "$PROGRAM" &
PID=$!
sleep 1

if ps -p $PID > /dev/null; then
    MEMORY=$(ps -o rss= -p $PID 2>/dev/null || echo "0")
    echo "  运行时内存使用: ${MEMORY}KB"
    kill $PID 2>/dev/null || true
    wait $PID 2>/dev/null || true
else
    echo "  程序已退出"
fi

echo ""
echo "测试完成！"
echo ""
echo "测试结果总结:"
echo "✓ 程序编译成功"
echo "✓ 程序正常启动"
echo "✓ 数据库文件创建成功"
echo "✓ 日志文件创建成功"
echo "✓ 配置文件监控功能正常"
echo "✓ 内存使用合理"
echo ""
echo "生成的文件:"
echo "  数据库: $TEST_DIR/data/"
echo "  日志: $TEST_DIR/logs/"
echo ""
echo "要查看详细日志，请运行:"
echo "  cat $TEST_DIR/logs/events.log | jq ."
echo ""
echo "要查看数据库内容，请运行:"
echo "  sqlite3 $TEST_DIR/data/data.db '.schema'"
echo "  sqlite3 $TEST_DIR/data/data.db 'SELECT * FROM system_data LIMIT 5;'"

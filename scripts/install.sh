#!/bin/bash

# 系统监控程序安装脚本

set -e

# 配置变量
PROGRAM_NAME="mon"
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="/etc/mon"
DATA_DIR="/var/lib/mon"
LOG_DIR="/var/log/mon"
SERVICE_FILE="/etc/systemd/system/mon.service"

echo "开始安装系统监控程序..."

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    exit 1
fi

# 创建必要的目录
echo "创建目录..."
mkdir -p "$CONFIG_DIR"
mkdir -p "$DATA_DIR"
mkdir -p "$LOG_DIR"

# 复制程序文件
echo "安装程序文件..."
if [ -f "target/release/$PROGRAM_NAME" ]; then
    cp "target/release/$PROGRAM_NAME" "$INSTALL_DIR/"
    chmod +x "$INSTALL_DIR/$PROGRAM_NAME"
else
    echo "错误: 找不到编译后的程序文件，请先运行 'cargo build --release'"
    exit 1
fi

# 复制配置文件
echo "安装配置文件..."
if [ -f "config.toml" ]; then
    cp "config.toml" "$CONFIG_DIR/"
else
    echo "警告: 找不到配置文件，将使用默认配置"
fi

# 创建systemd服务文件
echo "创建systemd服务..."
cat > "$SERVICE_FILE" << EOF
[Unit]
Description=System Resource Monitor
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=$INSTALL_DIR/$PROGRAM_NAME
Restart=always
RestartSec=10
Environment=MON_CONFIG_PATH=$CONFIG_DIR/config.toml
Environment=RUST_LOG=info

# 安全设置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$DATA_DIR $LOG_DIR
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF

# 设置权限
echo "设置权限..."
chown -R root:root "$CONFIG_DIR"
chown -R root:root "$DATA_DIR"
chown -R root:root "$LOG_DIR"
chmod 755 "$CONFIG_DIR"
chmod 755 "$DATA_DIR"
chmod 755 "$LOG_DIR"
chmod 644 "$CONFIG_DIR/config.toml" 2>/dev/null || true

# 重新加载systemd配置
echo "重新加载systemd配置..."
systemctl daemon-reload

# 启用服务
echo "启用服务..."
systemctl enable mon.service

echo "安装完成！"
echo ""
echo "使用以下命令管理服务："
echo "  启动服务: systemctl start mon"
echo "  停止服务: systemctl stop mon"
echo "  查看状态: systemctl status mon"
echo "  查看日志: journalctl -u mon -f"
echo ""
echo "配置文件位置: $CONFIG_DIR/config.toml"
echo "数据目录: $DATA_DIR"
echo "日志目录: $LOG_DIR"
echo ""
echo "要启动服务，请运行: systemctl start mon"

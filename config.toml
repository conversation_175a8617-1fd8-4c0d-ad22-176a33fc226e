# 系统监控程序配置文件

# 监控间隔（秒）
watch_interval = 60

# 数据保留天数
data_retention_days = 30

# 数据库文件路径
database_path = "/tmp/mon_test/data/data.db"

# 日志目录路径
log_path = "/tmp/mon_test/logs/"

# 预警阈值配置
[thresholds]
# CPU使用率阈值（百分比）
cpu_usage_warning = 80.0
cpu_usage_critical = 95.0

# 内存使用率阈值（百分比）
memory_usage_warning = 80.0
memory_usage_critical = 95.0

# 磁盘使用率阈值（百分比）
disk_usage_warning = 80.0
disk_usage_critical = 95.0

# inode使用率阈值（百分比）
inode_usage_warning = 80.0
inode_usage_critical = 95.0

# 温度阈值（摄氏度）
temperature_warning = 70.0
temperature_critical = 85.0

# 存储健康阈值（百分比）
storage_health_warning = 20.0
storage_health_critical = 10.0

# 监控功能配置
[monitoring]
# 启用CPU监控
enable_cpu = true

# 启用内存监控
enable_memory = true

# 启用磁盘监控
enable_disk = true

# 启用网络监控
enable_network = true

# 启用温度监控
enable_temperature = true

# 启用存储健康监控
enable_storage_health = true

# 启用文件系统监控
enable_filesystem = false

# 文件系统监控路径
filesystem_paths = [
    "/etc",
    "/var/log",
    "/home"
]

# 网络连通性检查主机
network_check_hosts = [
    "*******",
    "*******",
    "***************"
]
